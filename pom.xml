<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xinfei.springboot</groupId>
        <artifactId>springboot-parent</artifactId>
        <version>1.0.7.20231110</version>
    </parent>
    <groupId>com.xinfei.vocmng</groupId>
    <artifactId>vocmng</artifactId>
    <version>1.0.4.20230904</version>
    <packaging>pom</packaging>
    <properties>
        <xfframework.version>1.4.9.20250611</xfframework.version>
        <xfframework.mq.version>1.4.9.20250725.02</xfframework.mq.version>
        <cis-query-facade.version>20250401.2.RELEASE</cis-query-facade.version>
        <lendtrade-facade.version>1.0.7.20241208</lendtrade-facade.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <repaytrade-facade.version>1.0.0.20250722-SNAPSHOT</repaytrade-facade.version>
        <httpcomponents.version>4.5.13</httpcomponents.version>
        <jsonwebtoken.version>0.11.2</jsonwebtoken.version>
        <alibaba-dingtalk-service-sdk.version>2.0.0</alibaba-dingtalk-service-sdk.version>
        <dingtalk.version>1.4.78</dingtalk.version>
        <pinyin4j.version>2.5.0</pinyin4j.version>
        <easyexcel.version>3.3.3</easyexcel.version>
        <redisson.version>3.17.7</redisson.version>
        <vip-core.version>1.0.10.20250703</vip-core.version>
        <aliyun.sdk.oss.version>3.15.1</aliyun.sdk.oss.version>
        <javax.mail.version>1.6.2</javax.mail.version>
        <commons-compress.version>1.20</commons-compress.version>
        <commons-io.version>2.8.0</commons-io.version>
        <poi.version>4.1.2</poi.version>
        <pdf.version>2.0.27</pdf.version>
        <fundcore-facade.version>1.1.6.20241029</fundcore-facade.version>
        <dynamic-ds.version>3.5.2</dynamic-ds.version>
        <ssomng.version>20240911</ssomng.version>
        <ssocore.version>20240821</ssocore.version>
        <huttalegal-facade.version>2.0.1-SNAPSHOT</huttalegal-facade.version>
        <contractcore.version>1.0.4.20250522</contractcore.version>
        <psenginecore.version>1.0.1.20241209</psenginecore.version>
        <vqcprod.version>1.1.2-20240606</vqcprod.version>
        <swagger.ui.version>4.0.0</swagger.ui.version>
        <swagger.models.version>1.5.21</swagger.models.version>
        <supervip-interfaces.version>1.0.1.20250708</supervip-interfaces.version>
        <supervip-common.version>1.0.0.20250708</supervip-common.version>
        <arms.apm.version>1.7.3</arms.apm.version>
        <net.logstash.logback.version>6.6</net.logstash.logback.version>
    </properties>
    <modules>
        <module>dal</module>
        <module>biz</module>
        <module>itl</module>
        <module>util</module>
    </modules>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.xinfei.xfframework</groupId>
                <artifactId>xfframework-dependency</artifactId>
                <version>${xfframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.xfframework</groupId>
                <artifactId>xfframework-starter-mq</artifactId>
                <version>${xfframework.mq.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.xinfei.xfframework</groupId>-->
<!--                <artifactId>xfframework-starter-apollo</artifactId>-->
<!--                <version>${xfframework.apollo.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.xinfei.xfframework</groupId>
                <artifactId>xfframework-dependency-pom</artifactId>
                <version>${xfframework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.xinfei.vocmng</groupId>
                <artifactId>dal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.vocmng</groupId>
                <artifactId>biz</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.vocmng</groupId>
                <artifactId>itl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.vocmng</groupId>
                <artifactId>util</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>

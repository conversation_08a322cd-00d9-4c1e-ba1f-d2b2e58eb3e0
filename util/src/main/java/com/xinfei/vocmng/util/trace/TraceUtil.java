package com.xinfei.vocmng.util.trace;

import com.alibaba.arms.tracing.Span;
import com.alibaba.arms.tracing.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.cloud.commons.util.IdUtils;

import java.util.UUID;

/**
 * <AUTHOR>
 * @version $ ActivityTraceUtil, v 0.1 2025/3/17 10:11 shaohui.chen Exp $
 */
@Slf4j
public class TraceUtil {

    public static final String LOG_TRACE_ID = "Vocmng-TraceID";

    public static String getTraceId() {
        return MDC.get(LOG_TRACE_ID);
    }

    public static void initTraceId() {
        String traceId = getTraceId();
        try {
            if (StringUtils.isBlank(traceId)) {
                Span span = Tracer.builder().getSpan();
                traceId = span.getTraceId();
            }
        } catch (Exception e) {
            // do nothing
        }
        if (StringUtils.isBlank(traceId)) {
            traceId = UUID.randomUUID().toString().replaceAll("-", "");
        }
        MDC.put(LOG_TRACE_ID, traceId);
    }

    public static void clearTraceId() {
        MDC.clear();
    }

}

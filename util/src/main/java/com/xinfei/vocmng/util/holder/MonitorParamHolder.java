package com.xinfei.vocmng.util.holder;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 * @version $ MonitorParamHolder, v 0.1 2025/4/9 15:18 shaohui.chen Exp $
 */
public class MonitorParamHolder {

    private final static ThreadLocal<Map<String, String>> HOLDER = new ThreadLocal<>();

    public static Map<String, String> get() {
        return HOLDER.get();
    }

    public static void set(String key, String value) {
        Map<String, String> map = HOLDER.get();
        if (null == map) {
            map = Maps.newHashMap();
        }
        map.put(key, value);
        HOLDER.set(map);
    }

    public static void clear() {
        HOLDER.remove();
    }

}

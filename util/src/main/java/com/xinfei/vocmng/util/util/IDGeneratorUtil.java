package com.xinfei.vocmng.util.util;

import cn.hutool.core.util.IdUtil;

import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @version $ IDGeneratorUtil, v 0.1 2025/3/27 11:24 shaohui.chen Exp $
 */
public class IDGeneratorUtil {

    /**
     * 唯一性id
     * @param prefix 前缀
     */
    public static String genBase64Id(String prefix) {
        // 1) 生成Snowflake 64位ID
        long snowflakeId = IdUtil.getSnowflakeNextId();

        // 2) base62编码
        String base62 = Base62Util.encode(snowflakeId);

        // 3) 生成3位随机数(也可更多)
        int randomNum = ThreadLocalRandom.current().nextInt(100, 1000);

        // 4) 拼接
        return prefix + base62 + randomNum;
    }
}

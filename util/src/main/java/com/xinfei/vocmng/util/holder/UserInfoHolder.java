package com.xinfei.vocmng.util.holder;

import com.xinfei.ssocore.client.component.SsoUserInfoDTO;

/**
 * <AUTHOR>
 * @version $ UserInfoHolder, v 0.1 2025/3/19 17:48 shaohui.chen Exp $
 */
public class UserInfoHolder{
    private final static ThreadLocal<SsoUserInfoDTO> holder = new ThreadLocal<>();

    public static SsoUserInfoDTO getUserInfo() {
        return holder.get();
    }

    public static void setUserInfo(SsoUserInfoDTO userInfo) {
        holder.set(userInfo);
    }

    public static void clearUserInfo() {
        holder.remove();
    }


    public static String getStaffName() {
        SsoUserInfoDTO userInfo = getUserInfo();
        if (null == userInfo) {
            return null;
        }
        return userInfo.getFullName();
    }

    public static Long getUserId() {
        SsoUserInfoDTO userInfo = getUserInfo();
        if (null == userInfo) {
            return null;
        }
        return userInfo.getUserId();
    }
}

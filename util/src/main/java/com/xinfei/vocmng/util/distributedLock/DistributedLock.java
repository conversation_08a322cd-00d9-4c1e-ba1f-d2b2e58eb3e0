package com.xinfei.vocmng.util.distributedLock;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version $ DistributedLock, v 0.1 2025/3/20 17:56 shaohui.chen Exp $
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
@Inherited
@Documented
public @interface DistributedLock {

    /**
     * 锁名称，支持spEL，如："'abc'+#param"。常量部分用单引号括起来，变量部分前加"+"号
     */
    String lockName();

    /**
     * 加锁时长，单位为分钟
     */
    long lockTime() default 1;

    /**
     * 重试次数，
     */
    long retryTimes() default 1;
}

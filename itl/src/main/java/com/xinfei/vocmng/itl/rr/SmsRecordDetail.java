package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ SmsRecordDetail, v 0.1 2023/12/27 09:33 qu.lu Exp $
 */
@Data
public class SmsRecordDetail {
    /** 业务类型 */
    private String group;
    /** 短信内容 */
    private String content;
    /** 模板ID */
    private String source;
    /** 发送状态 */
    @JsonProperty(value = "delivery_status")
    private String deliveryStatus;
    /** 状态描述 */
    @JsonProperty(value = "delivery_msg")
    private String deliveryMsg;
    /** 使用渠道 */
    private String agent;
    /** 创建时间 */
    @JsonProperty(value = "created_time")
    private String createdTime;
    /** 模板：使用目的 */
    private String aim;
    /** 消息编号 */
    @JsonProperty(value = "batch_num")
    private String batchNo;
    /** 上游系统名称 */
    @JsonProperty(value = "ua")
    private String appName;
}

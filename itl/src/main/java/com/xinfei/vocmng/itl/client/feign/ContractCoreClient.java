package com.xinfei.vocmng.itl.client.feign;

import com.xinfei.contractcore.common.service.facade.request.contract.ContractProofApplyRequest;
import com.xinfei.contractcore.common.service.facade.request.query.ContractQueryRequest;
import com.xinfei.contractcore.common.service.facade.vo.ContractVO;

import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
public interface ContractCoreClient {


    /**
     * @param req:
     * @return String
     * <AUTHOR>
     * @description applyProof
     * @date 2024/12/24 17:30
     */
    String applyProof(ContractProofApplyRequest req);

    /**
     * @param req:
     * @return List<ContractVO>
     * <AUTHOR>
     * @description listQuery
     * @date 2024/12/24 17:30
     */
    List<ContractVO> listQuery(ContractQueryRequest req);

    /**
     * @param req:
     * @return String
     * <AUTHOR>
     * @description first query then apply
     * @date 2024/12/24 17:29
     */
    String applyProofBeforeQuery(ContractProofApplyRequest req);

}

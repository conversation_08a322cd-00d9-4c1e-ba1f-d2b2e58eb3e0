package com.xinfei.vocmng.itl.rr;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import lombok.Data;

/**
 * 优惠券接口响应通用格式
 *
 * <AUTHOR>
 * @version $ CouponResponse, v 0.1 2024/08/13 21:48 liupengming Exp $
 */

@Data
public class CouponResponse<T> {

    /**
     * status，返回1，其余失败场合有对应错误码
     */
    private Integer status;

    /**
     * 响应提示语
     */
    private String message;

    /**
     * 接口实际数据返回的承载字段
     */
    private T response;

    /**
     * 时间
     */
    private Integer time;

    /**
     * 服务响应是否成功：true成功
     *
     * @return
     */
    public boolean isSuccess() {
        return FeignConstants.COUPON_SUCCESS_CODE.equals(status);
    }

}

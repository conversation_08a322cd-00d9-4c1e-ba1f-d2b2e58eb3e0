package com.xinfei.vocmng.itl.client.feign;

import com.xinfei.cisaggs.facade.rr.StandardBatchCustomerInfoResponse;
import com.xyf.bank.dto.response.BankCardResponse;
import com.xyf.ext.info.model.Challenge;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ CisExtFacadeClient, v 0.1 2023/11/11 12:52 valiant.shaw Exp $
 * @Description: <a href="https://www.tapd.cn/********/markdown_wikis/show/#11********001017475">家庭教育/联系人信息/工作信息(标准接口)</a>
 */
public interface CisExtFacadeClient {

    /**
     * <a href="https://www.tapd.cn/********/markdown_wikis/show/#11********001017475@toc2">查询用户的家庭教育/工作信息/联系人信息(最近一次上传的联系人信息)</a>
     *
     * @param custNo 必填
     * @param app    必填
     * @return
     */
    Challenge queryLatestUserExtInfoData(String custNo, String app);

    /**
     * <a href="https://alidocs.dingtalk.com/i/nodes/KGZLxjv9VG34PKdwcokjByYkV6EDybno">传xyf会返回xyf及xyf01最新信息</a>
     *
     * @param custNo
     * @param app
     * @return
     */
    StandardBatchCustomerInfoResponse queryStandardBatchCustomerInfo(String custNo, String app ,List<String> appList);


    /**
     * <a href="https://www.tapd.cn/********/markdown_wikis/show/#11********001018829@toc8">根据卡id查询银行卡信息</a>
     *
     * @param custNo 必填
     * @param cardId 必填
     * @return
     */
    BankCardResponse queryBankCardInfoById(String custNo, Long cardId);

    /**
     * 获取银行卡信息列表
     *
     * @param custNo
     * @param app
     * @return
     */
    List<BankCardResponse> queryBankCardList(String custNo, String app);

}

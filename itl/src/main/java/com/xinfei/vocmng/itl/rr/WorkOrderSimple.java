package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 工单简要信息
 *
 * <AUTHOR>
 * @version $ WorkOrderSimple, v 0.1 2024/1/15 16:00 qu.lu Exp $
 */
@Data
public class WorkOrderSimple {
    /**  工单主键ID */
    private Integer id;
    /**  工单状态，0未提交分配，1待分配，2跟进中，3转派，4退单，5终止， 6知悉结案，7催收结案，8已结案，9失联结案，10不接受方案 */
    @JsonProperty(value = "task_status")
    private Integer status;
    /** 投诉渠道 */
    @JsonProperty(value = "task_from")
    private Integer source;
    /** 业务主体id */
    @JsonProperty(value = "scene_id")
    private Integer sceneId;
    /** 工单类型id */
    @JsonProperty(value = "task_type_id")
    private Integer typeId;
    /** 问题类型id */
    @JsonProperty(value = "question_type_id")
    private Integer questionTypeId;
}

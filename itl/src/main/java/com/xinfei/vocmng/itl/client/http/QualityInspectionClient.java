package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
@FeignClient(name = FeignConstants.QUALITY_INSPECTION, contextId = FeignConstants.QUALITY_INSPECTION, path = "/")
public interface QualityInspectionClient {

    /**
     * 批量添加组织信息
     *
     * @param req
     * @param authorization
     * @return
     */
    @PostMapping("/open/org/batch")
    QualityInspectionResponse<List<BathResponseData>> orgBath(QualityInspectionRequest<List<OrgData>> req, @RequestHeader(name = "Authorization") String authorization);

    /**
     * 批量添加员工信息
     *
     * @param req
     * @param authorization
     * @return
     */
    @PostMapping("/open/staff/batch")
    QualityInspectionResponse<List<BathResponseData>> staffBath(QualityInspectionRequest<List<StaffData>> req, @RequestHeader(name = "Authorization") String authorization);

    /**
     * 获取员工token
     *
     * @param staffId
     * @param authorization
     * @return
     */
    @GetMapping("/open/staff/{staffId}/token")
    QualityInspectionResponse<List<String>> token(@PathVariable("staffId") String staffId, @RequestHeader(name = "Authorization") String authorization);

    /**
     * 创建会话
     *
     * @param sessionsRequest
     * @param authorization
     * @return
     */
    @PostMapping("/api/gateway/rta/v1/sessions")
    SessionsResponse sessions(SessionsRequest sessionsRequest, @RequestHeader(name = "Authorization") String authorization);

    /**
     * 创建会话
     *
     * @param signalsRequest
     * @param authorization
     * @return
     */
    @PostMapping("/api/gateway/rta/v1/sessions/{callId}/signals")
    SessionsResponse signals(@PathVariable("callId") String callId, SignalsRequest signalsRequest, @RequestHeader(name = "Authorization") String authorization);
}

package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 聚合所有情况的response，适配系统response
 *
 * <AUTHOR>
 * @version $ UnifiedBaseResponse, v 0.1 2025/3/25 09:45 shaohui.chen Exp $
 */
@Data
public class UnifiedBaseResponse<T> {

    // Fields from XYF's BaseResponse
    @JsonAlias({"isSuccess", "success", "status"})
    @JsonProperty("isSuccess")
    private Boolean isSuccess;

    @JsonAlias({"code"})
    private String code;

    @JsonAlias({"msg", "message"})
    private String message;

    // Fields from AssetCore's BaseResponse
    @JsonAlias({"data", "response"})
    private T payload;

    @JsonIgnore
    public boolean isSuccess() {
        return Boolean.TRUE.equals(isSuccess) || StringUtils.equals("200", code);
    }

    @JsonIgnore
    public T getData() {
        return payload;
    }
}

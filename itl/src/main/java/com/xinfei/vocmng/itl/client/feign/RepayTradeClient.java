package com.xinfei.vocmng.itl.client.feign;

import com.xinfei.repaytrade.facade.rr.dto.ReductionDetailDto;
import com.xinfei.repaytrade.facade.rr.response.reduction.DetailListResponse;
import com.xinfei.repaytrade.facade.rr.response.reduction.ReductionAmountResponse;

import java.util.List;

/**
 * <AUTHOR> 2024/7/16 上午11:21
 * RepayTradeClient 抵扣
 */
public interface RepayTradeClient {

    DetailListResponse deductionDetailList(List<String> toLoanNo, String status);

    Boolean deductionDetailCancel(String reductionDetailId, String updatedBy);

    ReductionAmountResponse reductionAmount(String toLoanNo);

}

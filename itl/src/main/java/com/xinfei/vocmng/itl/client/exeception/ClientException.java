package com.xinfei.vocmng.itl.client.exeception;

import lombok.Getter;

/**
 * @version 1.0
 * @author：valiant.shaw
 * @date：2023/7/6 14:28
 */
@Getter
public class ClientException extends RuntimeException {


    private final TechplayErrDtlEnum errDtlEnum;
    //错误级别
    private final ErrorLevelsEnum errorLevel;

    public ClientException(TechplayErrDtlEnum codeEnums, Throwable throwable, ErrorLevelsEnum errorLevel) {
        super(codeEnums.getDescription(), throwable);
        this.errDtlEnum = codeEnums;
        this.errorLevel = errorLevel;
    }

    public ClientException(TechplayErrDtlEnum codeEnums, ErrorLevelsEnum errorLevel) {
        super(codeEnums.getDescription());
        this.errDtlEnum = codeEnums;
        this.errorLevel = errorLevel;
    }

    public ClientException(TechplayErrDtlEnum codeEnums, String msg, ErrorLevelsEnum errorLevel) {
        super(codeEnums.getDescription() + msg);
        this.errDtlEnum = codeEnums;
        this.errorLevel = errorLevel;
    }

    public ClientException(String msg) {
        super(msg);
        this.errDtlEnum = TechplayErrDtlEnum.CLIENT_CODE_ERROR;
        this.errorLevel = ErrorLevelsEnum.ERROR;
    }


}

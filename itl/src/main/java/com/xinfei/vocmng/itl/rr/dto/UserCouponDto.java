/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> 2024/8/12 18:10
 * 用戶优惠券 UserCouponDto
 */
@Data
public class UserCouponDto {
    @ApiModelProperty("优惠券id")
    private String couponId;

    @ApiModelProperty("优惠券名称")
    private String couponName;

    @ApiModelProperty("优惠券类型 1:借款免息券 2:还款立减金 3:限时提额券 4:拉卡拉聚合支付 5:x天免息券")
    private String couponType;

    @ApiModelProperty("优惠券状态 0:可使用 1:已使用 2:无效")
    private String status;

    @ApiModelProperty("使用明细")
    private String useDetail;

    @ApiModelProperty("抵扣手续费")
    private BigDecimal deductionUseDetailAmt;

    @ApiModelProperty("抵扣权益费")
    private BigDecimal deductionRightsAmt;

    @ApiModelProperty("使用时间")
    private String usedTime;

    @ApiModelProperty("发放时间")
    private String createdTime;
}
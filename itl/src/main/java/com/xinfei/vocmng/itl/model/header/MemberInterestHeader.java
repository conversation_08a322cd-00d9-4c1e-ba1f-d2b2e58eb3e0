package com.xinfei.vocmng.itl.model.header;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 会员权益公共请求头
 *
 * <AUTHOR>
 * @since 2023/12/20
 */
@Data
public class MemberInterestHeader extends BaseHeader {
    /** 应用名称，如：xyf 信用飞 */
    @JsonProperty(value = "App-Name")
    private String appName;
    /** 版本号 */
    @JsonProperty(value = "Version-Code")
    private Integer versionCode;
    /** 只支持系统：ios 苹果，android 安卓，wap */
    @JsonProperty(value = "Os")
    private String os;
    /** 鉴权ID */
    private String appId;
    /** 鉴权秘钥 */
    private String appSecret;
}

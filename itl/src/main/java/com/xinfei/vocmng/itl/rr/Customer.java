package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ Customer, v 0.1 2025/4/29 14:35 shaohui.chen Exp $
 */
@Data
public class Customer {
    private Long id;
    @JsonProperty("nick_name")
    private String nickName;
    private String level;
    private String description;
    @JsonProperty("owner_id")
    private Long ownerId;
    @JsonProperty("owner_group_id")
    private Long ownerGroupId;
    @JsonProperty("custom_fields")
    private Map<String, Object> customFields;
    @JsonProperty("open_api_token")
    private String openApiToken;
    @JsonProperty("organization_id")
    private Long organizationId;
    @JsonProperty("is_blocked")
    private boolean isBlocked;
    @JsonProperty("web_token")
    private String webToken;
    @JsonProperty("sdk_token")
    private String sdkToken;
    private List<Tag> tags;
    @JsonProperty("rich_tags")
    private List<RichTag> richTags;
    @JsonProperty("first_contact_at")
    private String firstContactAt;
    @JsonProperty("last_contact_at")
    private String lastContactAt;
    @JsonProperty("first_contact_at_via_phone")
    private String firstContactAtViaPhone;
    @JsonProperty("last_contact_at_via_phone")
    private String lastContactAtViaPhone;
    @JsonProperty("first_contact_at_via_im")
    private String firstContactAtViaIm;
    @JsonProperty("last_contact_at_via_im")
    private String lastContactAtViaIm;
    private String email;
    @JsonProperty("other_emails")
    private List<Object> otherEmails;
    private List<Cellphone> cellphones;
    private String platform;
    @JsonProperty("source_channel")
    private String sourceChannel;
    private List<Weixin> weixins;
    @JsonProperty("weixin_minis")
    private List<WeixinMini> weixinMinis;
    @JsonProperty("weixin_works")
    private List<WeixinWork> weixinWorks;

    @Data
    public static class Tag {
        private Long id;
        private String name;
        @JsonProperty("company_id")
        private Long companyId;
    }

    @Data
    public static class RichTag {
        private Long id;
        private String name;
        private String color;
        @JsonProperty("company_id")
        private Long companyId;
    }

    @Data
    public static class Cellphone {
        private Long id;
        private String content;
    }

    @Data
    public static class Weixin {
        private String appid;
        private String openid;
        private String unionid;
    }

    @Data
    public static class WeixinMini {
        private String appid;
        private String openid;
        private String unionid;
    }

    @Data
    public static class WeixinWork {
        private String agentid;
        private String corpid;
        private String userid;
        @JsonProperty("open_userid")
        private String openUserid;
    }
}

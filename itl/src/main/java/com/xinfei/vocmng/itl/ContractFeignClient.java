package com.xinfei.vocmng.itl;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@FeignClient(name = FeignConstants.CONTRACT,contextId = FeignConstants.CONTRACT+".PhpFeinClient",path = "/")
public interface ContractFeignClient {
    /**
     * 根据合同号查询子合同列表信息（营收订单）
     * 接口文档：https://www.tapd.cn/20090981/markdown_wikis/show/?left_tree=1#1120090981001003834
     * @param request
     * @return
     */
    @PostMapping("/v2/cash/sub-contract-list")
    ContractResponse<SubContractList<ContractDetail>> queryCashSubContractList(ContractBaseRequest<CashSubContractListRequest> request);


    /**
     * 根据合同号获取合同下载链接信息
     * 接口文档：https://www.tapd.cn/20090981/markdown_wikis/show/?left_tree=1#1120090981001003791
     * @param request
     * @return
     */
    @PostMapping("/v2/cash/get-sub-contract-down-list")
    ContractResponse<SubContractList<ContractDownloadDetail>> queryCashSubContractDownloadInfo(ContractBaseRequest<ContractDownloadRequest> request);


    @PostMapping("/fund/fund-order-query")
    ContractResponse<FundOrderQueryReq> fundOrderQuery(ContractBaseRequest<FundOrderQueryReq> request);

    /**
     *生成并获取下载地址结清证明  https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001005637
     * @date 2024/6/4 16:52
     */
    @PostMapping("/v2/other/generate-and-sign-and-get-download-url")
    ContractResponse<DownloadUrlResponse> querySettleUrl(ContractBaseRequest<DownloadUrlReq> request);

    /**
     *  https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001016718
     */
    @PostMapping("/v2/contract/get-one-user-order-contract")
    ContractResponse<UserOrderContractResp> getOneUserOrderContract(ContractBaseRequest<UserOrderContractReq> request);

    /**
     *  https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001016718
     */
    @PostMapping("/v2/contract/get-all-user-order-contract")
    ContractResponse<List<UserOrderContractResp>> getAllUserOrderContract(ContractBaseRequest<UserOrderContractReq> request);

    /**
     *  https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001015075
     */
    @PostMapping("/v2/contract/credit-investigation-list")
    ContractResponse<List<InvestigationResp>> creditInvestigationList(ContractBaseRequest<InvestigationReq> request);

    /**
     *  https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001015355
     */
    @PostMapping("/v2/contract-union-rule/get-group-by-key-all")
    ContractResponse<List<GroupKeyResp>> getGroupByKeyAll(ContractBaseRequest<GroupKeyReq> request);

    /**
     *  https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001015355
     */
    @PostMapping("/v2/contract-cash-rule-template-config/get-group-by-key-all")
    ContractResponse<List<GroupKeyResp>> getCashGroupByKeyAll(ContractBaseRequest<GroupKeyReq> request);

    @PostMapping("/v2/cash/re-sign")
    ContractResponse<Object> cashReSign(ContractBaseRequest<ReSignReq> request);

    @PostMapping("/v2/consume/re-sign")
    ContractResponse<Object> consumeReSign(ContractBaseRequest<ReSignReq> request);


}

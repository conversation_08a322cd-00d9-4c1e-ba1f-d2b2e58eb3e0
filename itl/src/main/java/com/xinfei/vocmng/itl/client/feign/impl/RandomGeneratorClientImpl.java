/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import cn.xinfei.usergroup.random.client.AbClient;
import cn.xinfei.usergroup.random.client.bo.AbBO;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ RandomGeneratorClientImpl, v 0.1 2026-06-18 14:10 pengming.liu Exp $
 */

@Component
@Slf4j
public class RandomGeneratorClientImpl {
    public String ab(String userNo, String bizKey) {
        AbBO response = null;
        String msg = "AbClient.ab:";
        try {
            response = AbClient.ab(biz<PERSON>ey, userNo);
            log.info(LogUtil.clientLog("AbClient", "ab", bizKey, response));
            return response.getGroup();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("AbClient", "ab", bizKey, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg+e.getMessage(), ErrorLevelsEnum.ERROR);
        }
    }

}
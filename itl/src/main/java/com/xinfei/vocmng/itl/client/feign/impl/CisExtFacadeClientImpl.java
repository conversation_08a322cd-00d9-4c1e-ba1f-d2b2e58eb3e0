/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.cisaggs.facade.CisFacade;
import com.xinfei.cisaggs.facade.rr.StandardBatchCustomerInfoRequest;
import com.xinfei.cisaggs.facade.rr.StandardBatchCustomerInfoResponse;
import com.xinfei.cisaggs.facade.rr.dto.CustomerQueryDTO;
import com.xinfei.cisaggs.facade.rr.dto.StandardCustInfoQueryItem;
import com.xinfei.cisaggs.facade.rr.dto.StandardUserInfoQueryItem;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.CisExtFacadeClient;
import com.xinfei.vocmng.itl.util.CisCommonAttributes;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xyf.bank.dto.request.QueryBankCardInfoByIdRequest;
import com.xyf.bank.dto.request.QueryBankCardInfoRequest;
import com.xyf.bank.dto.response.BankCardResponse;
import com.xyf.bank.facade.BankCardFacade;
import com.xyf.ext.info.dto.request.QueryChallengeRequest;
import com.xyf.ext.info.facade.ExtInfoFacade;
import com.xyf.ext.info.model.Challenge;
import com.xyf.user.facade.common.model.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ CisFacadeClientImpl, v 0.1 2023-12-20 14:10 junjie.yan Exp $
 */

@Component
@Slf4j
public class CisExtFacadeClientImpl implements CisExtFacadeClient {

    @Resource
    private ExtInfoFacade extInfoFacade;

    @Resource
    private BankCardFacade bankCardFacade;

    @Resource
    private CisFacade cisFacade;

    @Override
    public Challenge queryLatestUserExtInfoData(String custNo, String app) {
        BaseResponse<Challenge> response = null;
        QueryChallengeRequest request = new QueryChallengeRequest();
        request.setApp(app);
        request.setCustNo(custNo);
        CisCommonAttributes.setCommonAttributes(request);
        String msg = "ExtInfoFacade.queryLatestUserExtInfoData:";
        try {
            response = extInfoFacade.queryLatestUserExtInfoData(request);
            log.info(LogUtil.clientLog("ExtInfoFacade", "queryLatestUserExtInfoData", request, response));
            if (response == null || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ExtInfoFacade", "queryLatestUserExtInfoData", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public StandardBatchCustomerInfoResponse queryStandardBatchCustomerInfo(String custNo, String app ,List<String> appList) {
        BaseResponse<StandardBatchCustomerInfoResponse> response = null;
        StandardBatchCustomerInfoRequest request = new StandardBatchCustomerInfoRequest();
        List<CustomerQueryDTO> customerQueryDTOList = new ArrayList<>();
        if(!StringUtils.isEmpty(app)){
            CustomerQueryDTO customerQueryDTO = new CustomerQueryDTO();
            customerQueryDTO.setCustNo(custNo);
            if ("xyf".equals(app)) {
                customerQueryDTO.setApp("xyf01");
            } else {
                customerQueryDTO.setApp(app);
            }
            customerQueryDTOList.add(customerQueryDTO);
            request.setCustQueryItems(Arrays.asList(StandardCustInfoQueryItem.BASE_INFO_RESULT, StandardCustInfoQueryItem.CONTACT_RESULT));
        }else if(CollectionUtils.isNotEmpty(appList)){
            //获取用户关信息
            customerQueryDTOList = appList.stream()
                    .map(appName -> {
                        CustomerQueryDTO dto = new CustomerQueryDTO();
                        dto.setCustNo(custNo);
                        dto.setApp(appName);
                        return dto;
                    })
                    .collect(Collectors.toList());
            request.setUserQueryItems(Arrays.asList(StandardUserInfoQueryItem.USER_BASIC_INFO));
        }
        request.setRequestList(customerQueryDTOList);
        CisCommonAttributes.setCommonAttributes(request);
        String msg = "CisFacade.queryStandardBatchCustomerInfo:";
        try {
            response = cisFacade.queryStandardBatchCustomerInfo(request);
            log.info(LogUtil.clientLog("CisFacade", "queryStandardBatchCustomerInfo", request, response));
            if (response == null || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("CisFacade", "queryStandardBatchCustomerInfo", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public BankCardResponse queryBankCardInfoById(String custNo, Long cardId) {
        BaseResponse<BankCardResponse> response = null;
        QueryBankCardInfoByIdRequest request = new QueryBankCardInfoByIdRequest();
        request.setCardId(cardId);
        request.setCustNo(custNo);
        String msg = "BankCardFacade.queryBankCardInfoById:";
        try {
            response = bankCardFacade.queryBankCardInfoById(request);
            log.info(LogUtil.clientLog("BankCardFacade", "queryBankCardInfoById", request, response));
            if (response == null || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("BankCardFacade", "queryBankCardInfoById", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<BankCardResponse> queryBankCardList(String custNo, String app) {
        BaseResponse<List<BankCardResponse>> response = null;
        QueryBankCardInfoRequest request = new QueryBankCardInfoRequest();
        request.setCustNo(custNo);
        String msg = "BankCardFacade.queryBankCardList:";
        try {
            response = bankCardFacade.queryBankCardList(request);
            log.info(LogUtil.clientLog("BankCardFacade", "queryBankCardList", request, response));
            if (response == null || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("BankCardFacade", "queryBankCardList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }
}
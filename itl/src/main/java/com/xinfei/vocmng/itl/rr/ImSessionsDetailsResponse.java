/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ ImSessionsDetailsResponse, v 0.1 2024-12-18 16:28 pengming.liu Exp $
 */
@Data
public class ImSessionsDetailsResponse {
    /** 接口返回值中对应的id */
    @JsonProperty("id")
    private Long bizId;

    /** 所属会话 id */
    @JsonProperty("session_id")
    private Long sessionId;

    /** 所属子会话 id */
    @JsonProperty("sub_session_id")
    private Long subSessionId;

    /** 发送人 id */
    @JsonProperty("user_id")
    private Long userId;

    /** 发送人身份，agent 或 customer */
    @JsonProperty("sender")
    private String sender;

    /** 满意度调查结果 id */
    @JsonProperty("survey_option_id")
    private Long surveyOptionId;

    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    /** 消息内容 */
    @JsonProperty("content")
    private String content;
}
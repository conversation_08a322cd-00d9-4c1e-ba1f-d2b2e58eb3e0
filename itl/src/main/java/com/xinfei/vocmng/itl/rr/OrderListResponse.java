/**
 * Copyright 2023 bejson.com
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Auto-generated: 2023-12-27 15:35:50
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
@Data
public class OrderListResponse {

    private List<Order> list;
    @JsonProperty("total_count")
    private int totalCount;
    private int page;
    @JsonProperty("page_size")
    private int pageSize;
    @JsonProperty("total_page")
    private int totalPage;
}
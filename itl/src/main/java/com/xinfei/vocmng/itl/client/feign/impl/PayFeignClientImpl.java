/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.PayFeignClient;
import com.xinfei.vocmng.itl.rr.BankListResponse;
import com.xinfei.vocmng.itl.rr.UaRequest;
import com.xinfei.vocmng.itl.rr.VoucherGenerateRequest;
import com.xinfei.vocmng.itl.rr.VoucherGenerateResponse;
import com.xinfei.vocmng.itl.rr.dto.BankDto;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ PayFeignClientImpl, v 0.1 2024-04-16 14:32 junjie.yan Exp $
 */
@Component
@Slf4j
public class PayFeignClientImpl {

    @Resource
    private PayFeignClient payFeignClient;

    public List<BankDto> bankList() {
        UaRequest uaRequest = new UaRequest();
        BankListResponse response = null;
        String msg = "PayFeignClient.bankList:";
        try {
            uaRequest.setUa("vocmng");
            uaRequest.setAllFlag("1");
            response = payFeignClient.bankList(uaRequest);
            log.info(LogUtil.clientLog("PayFeignClient", "bankList", uaRequest, response));
            if (Objects.isNull(response) || !"000000".equals(response.getCode()) || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("PayFeignClient", "bankList", uaRequest, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 凭证生成
     * @param request 凭证生成请求
     * @return 凭证生成响应
     */
    public VoucherGenerateResponse generateVoucher(VoucherGenerateRequest request) {
        VoucherGenerateResponse response = null;
        String msg = "PayFeignClient.generateVoucher:";
        try {
            response = payFeignClient.generateVoucher(request);
            log.info(LogUtil.clientLog("PayFeignClient", "generateVoucher", request, response));
            if (Objects.isNull(response) || !"000000".equals(response.getCode())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("PayFeignClient", "generateVoucher", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 凭证生成
     * @param request 凭证生成请求
     * @return 凭证生成响应
     */
    public VoucherGenerateResponse queryVoucher(VoucherGenerateRequest request) {
        VoucherGenerateResponse response = null;
        String msg = "PayFeignClient.queryVoucher:";
        try {
            response = payFeignClient.queryVoucher(request);
            log.info(LogUtil.clientLog("PayFeignClient", "queryVoucher", request, response));
            if (Objects.isNull(response) || !"000000".equals(response.getCode())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("PayFeignClient", "queryVoucher", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }
}
/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.util;

import com.xinfei.xfframework.common.JsonUtil;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR> @version $ LogUtil, v 0.1 2023/11/30 15:24 ****.**** Exp $
 */

public class LogUtil {

    public static String infoLog(String title, Object content) {
        Map<String, Object> data = new LinkedHashMap<>();
        data.put("title", title);
        data.put("content", content);
        return JsonUtil.toJson(data);
    }

    public static String infoLog(String title, Object... args) {
        Map<String, Object> data = new LinkedHashMap<>();
        data.put("title", title);
        data.put("content", args);
        return JsonUtil.toJson(data);
    }

    public static String strategyInfoLog(String title, String loanNo, Object value, Object content) {
        Map<String, Object> data = new LinkedHashMap<>();
        data.put("strategyId", title);
        data.put("loanNo", loanNo);
        data.put("keyValues", value);
        data.put("result", content);
        return JsonUtil.toJson(data);
    }

    public static String newInfoLog(String title, String key, String value, Object content) {
        Map<String, Object> data = new LinkedHashMap<>();
        data.put("title", title);
        data.put(key, value);
        data.put("content", content);
        return JsonUtil.toJson(data);
    }

    /**
     * client 日志数据json
     */
    public static String clientLog(String serverName, String method, Object request, Object response,
                                   Map<String, Object> others) {
        Map<String, Object> data = new LinkedHashMap<>();
        data.put("serverName", serverName);
        data.put("serverMethod", method);
        data.put("request", request);
        data.put("response", response);
        if (others != null && !others.isEmpty()) {
            data.putAll(others);
        }

        return JsonUtil.toJson(data);
    }

    /**
     * 失败日志
     */
    public static String clientErrorLog(String className, String classMethod, String serverName, String serverMethod, Object request, Object response, Object eMessage) {
        Map<String, Object> others = new LinkedHashMap<>();
        others.put("className", className);
        others.put("classMethod", classMethod);
        others.put("eMessage", eMessage);
        return clientLog(serverName, serverMethod, request, response, others);
    }

    public static String clientErrorLog(String serverName, String method, Object request, Object response, Object eMessage) {
        Map<String, Object> others = new LinkedHashMap<>();
        others.put("eMessage", eMessage);

        return clientLog(serverName, method, request, response, others);
    }

    /**
     * client 日志  成功
     */
    public static String clientLog(String serverName, String method, Object request, Object response) {
        return clientLog(serverName, method, request, response, new LinkedHashMap<>());
    }

    /**
     * 日志 -包含header 失败日志
     */
    public static String clientLog(String serverName, String method, Map<String, String> header,
                                   Object request, Object response, Object errData) {
        Map<String, Object> others = new LinkedHashMap<>();
        others.put("header", header);
        others.put("errData", errData);

        return clientLog(serverName, method, request, response, others);
    }

    /**
     * 日志 -包含header
     */
    public static String clientLog(String serverName, String method, String header,
                                   Object request, Object response) {
        Map<String, Object> others = new LinkedHashMap<>();
        others.put("header", header);

        return clientLog(serverName, method, request, response, others);
    }


    /**
     * 失败日志 -包含header
     */
    public static String clientErrorLog(String serverName, String method, String header, Object request, Object response, Object eMessage) {
        Map<String, Object> others = new LinkedHashMap<>();
        others.put("header", header);
        others.put("eMessage", eMessage);

        return clientLog(serverName, method, request, response, others);
    }

    /**
     * 日志 -包含header
     */
    public static String clientLog(String serverName, String method, Map<String, String> header,
                                   Object request, Object response) {
        Map<String, Object> others = new LinkedHashMap<>();
        others.put("header", header);

        return clientLog(serverName, method, request, response, others);
    }
}
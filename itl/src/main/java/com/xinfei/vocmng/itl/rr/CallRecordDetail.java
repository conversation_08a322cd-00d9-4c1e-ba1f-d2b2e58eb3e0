package com.xinfei.vocmng.itl.rr;

import lombok.Data;

/**
 * 话单明细信息
 *
 * <AUTHOR>
 * @version $ CallRecordDetail, v 0.1 2023/12/27 21:52 qu.lu Exp $
 */
@Data
public class CallRecordDetail {
    /** 分配给外部系统的id */
    private String appKey;
    /** 与genesys交互的批次号 预测式外呼才有 */
    private String genesysBatchNo;
    /** genesys的callId */
    private String callId;
    /** 坐席编号 */
    private String agentNo;
    /** 坐席名称 */
    private String agentName;
    /** genesys坐席号 */
    private String agentId;
    /** 组织名称 */
    private String orgName;
    /** 技能组编号 */
    private String skillGroupNo;
    /** 技能组名称 */
    private String skillGroupName;
    /** 呼叫类型：1-呼出，2-呼入 */
    private String callType;
    /** 呼叫类型名称 */
    private String callTypeName;
    /** 外呼类型：1-点呼，2-预测外呼，3-AI 4-IVR */
    private String outboundType;
    /** 外呼类型名称 */
    private String outboundTypeName;
    /** 手机号 */
    private String originMobile;
    /** 外呼的手机号，需要拼接网关前缀 */
    private String callMobile;
    /** 业务线 */
    private String bizLine;
    /** 业务线名称 */
    private String bizLineName;
    /** 业务来源编码 */
    private String bizSourceCode;
    /** 业务来源名称 */
    private String bizSourceName;
    /** 线路组编码 */
    private String lineGroupNo;
    /** 落地网关名称 */
    private String gatewayName;
    /** 线路名称 */
    private String lineName;
    /** 拨打开始时间  yyyy-MM-dd HH:mm:ss */
    private String callStartTime;
    /** 拨打结束时间 yyyy-MM-dd HH:mm:ss */
    private String callEndTime;
    /** 坐席振铃时长 单位秒 */
    private String tAgentAlert;
    /** 用户振铃时长，单位秒 */
    private String tUserAlert;
    /** 通话时长，单位秒 */
    private String tConnect;
    /** 每个业务系统，每个批次，的唯一id */
    private String phoneId;
    /** 外呼结果,1-接通，2-未接通，3-溢出，4-未拨打 */
    private String callResult;
    /** 外呼结果名称 */
    private String callResultName;
    /** 外呼结果code */
    private String finishCause;
    /** 外呼结果描述 */
    private String finishCauseName;
    /** 创建时间 yyyy-MM-dd HH:mm:ss */
    private String createdTime;
    /** 修改时间 yyyy-MM-dd HH:mm:ss */
    private String updatedTime;
}

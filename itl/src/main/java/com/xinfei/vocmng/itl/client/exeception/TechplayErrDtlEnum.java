/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.exeception;

import lombok.Getter;

/**
 * 错误码明显段定义枚举
 *
 * <p>本枚举的code对应于标准错误码10~12位。
 * 而errorLevel对应于标准错误码的第4位
 *
 * <p>在标准错误码的位置如下：
 *     <table border="1">
 *     <tr>
 *     <td>位置</td><td>1</td><td>2</td><td>3</td><td bgcolor="yellow">4</td><td>5</td><td>6</td><td>7</td><td>8</td><td>9</td><td bgcolor="red">10</td><td bgcolor="red">11</td><td bgcolor="red">12</td>
 *     </tr>
 *     <tr>
 *     <td>示例</td><td>X</td><td>E</td><td>0</td><td>1</td><td>0</td><td>1</td><td>0</td><td>1</td><td>1</td><td>0</td><td>2</td><td>7</td>
 *     </tr>
 *     <tr>
 *     <td>说明</td><td colspan=2>固定<br>标识</td><td>规<br>范<br>版<br>本</td><td>错<br>误<br>级<br>别</td><td>错<br>误<br>类<br>型</td><td colspan=4>错误场景</td><td colspan=3>错误编<br>码</td>
 *     </tr>
 *     </table>
 *
 * <p>错误明细码的CODE取值空间如下：
 * <ul>
 *     <li>公共类错误码[000-099,999]
 *     <li>事务管理类错误码[100-149]
 *     <li>支用还款等交易类错误码[150-245]
 *     <li>日终处理类错误码[250-299]
 *     <li>查询类错误码[300-349]
 *     <li>管理类错误码[350-399]
 * </ul>
 *
 * <AUTHOR>
 * @version $ TechplayErrDtlEnum, v 0.1 2023/8/28 17:42 Jinyan.Huang Exp $
 */
@Getter
public enum TechplayErrDtlEnum {

    //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\//
    //                      公共类错误码[000-099,999]                             //
    //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\//
    /**
     * 其它未知异常
     */
    UNKNOWN_EXCEPTION("999", ErrorLevelsEnum.ERROR, "其它未知异常"),
    NO_LOGIN("400001", ErrorLevelsEnum.ERROR, "您的登录状态已失效，请重新登录"),
    FLOW_NUM_LOGIN("400002", ErrorLevelsEnum.ERROR, "流水号不能为空，请重新登录"),
    ILLEGAL_ARGUMENT("999", ErrorLevelsEnum.ERROR, "不合法的参数异常"),
    ATTRIBUTES_IS_NULL("400002", ErrorLevelsEnum.ERROR, "attributes is null，请重新登录"),

    /**
     * 配置错误
     */
    CONFIGURATION_ERROR("001", ErrorLevelsEnum.FATAL, "配置错误"),

    /**
     * 数据库异常
     */
    DB_EXCEPTION("002", ErrorLevelsEnum.ERROR, "数据库异常"),

    /**
     * 数据更新异常
     */
    DATA_UPDATE_EXCEPTION("003", ErrorLevelsEnum.ERROR, "数据库异常"),

    //参数错误
    PARAS_ERROR("004", ErrorLevelsEnum.ERROR, "参数错误"),
    INTERCEPTOR_ERROR("005", ErrorLevelsEnum.ERROR, "拦截器异常"),
    RATE_LIMIT_EXCEEDED("006", ErrorLevelsEnum.WARN, "业务限流异常"),



    //========================================================================//
    //                              业务处理类                                  //
    //========================================================================//
    //服务调用请求信息不合法
    REQ_PARAM_NOT_VALID("101", ErrorLevelsEnum.WARN, "服务调用请求信息不合法-"),
    CLIENT_CODE_ERROR("102", ErrorLevelsEnum.ERROR, "第三方客户端返回状态异常-"),
    CLIENT_SYS_ERROR("103", ErrorLevelsEnum.ERROR, "当前人数较多,请稍后再试"),


    LOCK_ERROR("307", ErrorLevelsEnum.ERROR, "系统繁忙-"),
    SMS_SEND_ERROR("306", ErrorLevelsEnum.ERROR, "短信中心，发送短信异常，请稍后再试"),

    FLOW_INFO_ERROR("104", ErrorLevelsEnum.ERROR, "流程信息表-配置异常-"),
    FLOW_INFO_GRAY_ERROR("105", ErrorLevelsEnum.ERROR, "流程信息表-灰度配置异常-"),
    FLOW_CONTROL_ERROR("106", ErrorLevelsEnum.ERROR, "流程控制表-配置异常-"),
    FLOW_CONTROL_HEAD_ERROR("107", ErrorLevelsEnum.ERROR, "流程控制表-配置异常,头节点不存在或配置多个-"),
    FLOW_RULE_ERROR("108", ErrorLevelsEnum.ERROR, "流程规则表-配置异常-"),
    FLOW_RULE_EQUAL_ERROR("109", ErrorLevelsEnum.ERROR, "流程规则表-只支持规则结果“=”的比较-"),
    FLOW_NO_SUN_RULE_ERROR("110", ErrorLevelsEnum.ERROR, "流程规则表配置有误-没有待执行的子规则-"),
    FLOW_PAGE_RULE_ERROR("110", ErrorLevelsEnum.ERROR, "流程规则表配置有误-不能同时命中规则和页面-"),
    FLOW_MORE_PAGE_ERROR("111", ErrorLevelsEnum.ERROR, "流程规则表配置有误-执行结果不能是多个页面-"),
    FLOW_PAGE_ERROR("112", ErrorLevelsEnum.ERROR, "流程页面表-配置异常-"),
    FLOW_CONTROL_RULE_HANDEL_ERROR("113", ErrorLevelsEnum.ERROR, "流程规则配置异常,只有规则才执行-"),
    FLOW_CONTROL_PAGE_RESULT_ERROR("114", ErrorLevelsEnum.ERROR, "流程控制系统异常"),


    FLOW_ORDER_CREATE_ERROR("201", ErrorLevelsEnum.ERROR, "订单操作-创建订单异常"),
    FLOW_ORDER_VERIFY_ERROR("202", ErrorLevelsEnum.ERROR, "订单操作-验证订单异常"),
    FLOW_ORDER_PROGRESS_ERROR("203", ErrorLevelsEnum.ERROR, "订单操作-订单进度查询异常"),
    FLOW_ORDER_USER_AGAIN_LOAN_ERROR("204", ErrorLevelsEnum.ERROR, "订单操作-查询用户状态异常"),
    FLOW_ORDER_COUPON_TRAIL_ERROR("205", ErrorLevelsEnum.ERROR, "订单操作-计算优惠券异常"),

    CIS_CLIENT_THREE_ELEMENTS_ERROR("300", ErrorLevelsEnum.ERROR, "用户操作-查询用户三要素异常"),
    CIS_CLIENT_CUS_NO_ERROR("301", ErrorLevelsEnum.ERROR, "用户操作-查询客户号信息异常"),
    CIS_CLIENT_CUS_NO_INFO_ERROR("302", ErrorLevelsEnum.ERROR, "用户操作-查询客户信息异常"),
    CIS_CLIENT_CREATE_CUS_ERROR("303", ErrorLevelsEnum.ERROR, "用户操作-创建客户异常"),
    CIS_CLIENT_BIND_CUS_ERROR("304", ErrorLevelsEnum.ERROR, "用户操作-绑定客户异常"),

    /**
     * 发送验证码
     */
    SEND_VERIFY_CODE_ERROR("305", ErrorLevelsEnum.ERROR, "签约绑卡发送验证码失败，请重试"),
    /**
     * 银行卡相关
     */
    BANK_CARD_ERROR("306", ErrorLevelsEnum.ERROR, "银行卡不存在，请重试或者重新绑定"),
    /**
     * 身份证相关
     */
    ID_CARD_CARD_ERROR("307", ErrorLevelsEnum.ERROR, "身份证不存在，请重试或者重新绑定"),
    /**
     * 姓名
     */
    NAME_ERROR("308", ErrorLevelsEnum.ERROR, "用户姓名不存在，请重试或者重新绑定"),
    /**
     * 手机号
     */
    MOBILE_ERROR("309", ErrorLevelsEnum.ERROR, "手机号不存在，请重试或者重新绑定"),

    /**
     * 绑卡验证次数频繁
     */
    BANK_CARD_REPEAT_LIMIT("310", ErrorLevelsEnum.ERROR, "操作太频繁，请稍后重试"),

    REPAY_ERROR("252", ErrorLevelsEnum.ERROR, "还款错误"),

    ;

    // ~~~ 属性定义 ~~~

    /**
     * 枚举编码
     */
    private final String code;

    /**
     * 错误级别
     */
    private final ErrorLevelsEnum errorLevel;

    /**
     * 描述说明
     */
    private final String description;

    /**
     * 私有构造函数。
     *
     * @param code        枚举编码
     * @param errorLevel  错误级别
     * @param description 描述说明
     */
    private TechplayErrDtlEnum(String code, ErrorLevelsEnum errorLevel, String description) {
        this.code = code;
        this.errorLevel = errorLevel;
        this.description = description;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    // ~~~容器方法 ~~~

    /**
     * @return Returns the description.
     */
    public String getDescription() {
        return description;
    }

    /**
     * Getter method for property <tt>errorLevvel</tt>.
     *
     * @return property value of errorLevvel
     */
    public ErrorLevelsEnum getErrorLevel() {
        return errorLevel;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code 枚举编码
     * @return 支付错误明细枚举
     */
    public static TechplayErrDtlEnum getByCode(String code) {
        for (TechplayErrDtlEnum detailCode : values()) {
            if (detailCode.getCode().equals(code)) {

                return detailCode;
            }
        }
        return null;
    }
}

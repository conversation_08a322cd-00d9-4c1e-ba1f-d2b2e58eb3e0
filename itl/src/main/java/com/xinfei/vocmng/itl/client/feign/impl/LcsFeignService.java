/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.LcsFeignClient;
import com.xinfei.vocmng.itl.rr.PlanDetailResponse;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.util.LogUtil;
import io.kyoto.pillar.lcs.loan.domain.LoanBatchQueryRequest;
import io.kyoto.pillar.lcs.loan.domain.LoanQueryResponse;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ LcsFeignService, v 0.1 2023-12-23 14:10 junjie.yan Exp $
 */

@Slf4j
@Component
public class LcsFeignService {
    @Resource
    private LcsFeignClient lcsFeignClient;

    public List<LoanQueryResponse> queryLoanQuery(LoanBatchQueryRequest request) {

        BatchLoanQueryResponse response = null;
        String msg = "LcsFeignClient.batchLoanQuery:";
        try {
            if (Objects.isNull(request)) {
                return null;
            }
            response = lcsFeignClient.batchLoanQuery(request);
            log.info(LogUtil.clientLog("LcsFeignClient", "batchLoanQuery", request, response));
            if (Objects.isNull(response) || !"S".equals(response.getFlag()) || !"0000".equals(response.getCode()) || Objects.isNull(response.getData())) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("LcsFeignClient", "batchLoanQuery", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<LoanPlanResponse> planDetail(LoanPlanRequest request) {

        PlanDetailResponse response = null;
        String msg = "LcsFeignClient.planDetail:";
        try {
            if (Objects.isNull(request)) {
                return null;
            }
            response = lcsFeignClient.planDetail(request);
            log.info(LogUtil.clientLog("LcsFeignClient", "planDetail", request, response));
            if (Objects.isNull(response) || !"S".equals(response.getFlag()) || !"0000".equals(response.getCode()) || Objects.isNull(response.getData())) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("LcsFeignClient", "planDetail", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<QueryLoanDetailResp> queryLoanInfo(QueryLoanReq request) {
        QueryLoanResp response = null;
        String msg = "LcsFeignClient.queryLoanInfo:";
        try {
            if (Objects.isNull(request)) {
                return null;
            }
            response = lcsFeignClient.queryLoanInfo(request);
            log.info(LogUtil.clientLog("LcsFeignClient", "queryLoanInfo", request, response));
            if (Objects.isNull(response) || !"S".equals(response.getFlag()) || !"0000".equals(response.getCode())) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("LcsFeignClient", "queryLoanInfo", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }
}
package com.xinfei.vocmng.itl.util;

import java.util.concurrent.atomic.AtomicLong;

public class SerialNumberGeneratorUtil {
    private static final AtomicLong serialNumber = new AtomicLong(0);

    public static String generate(String type) {
        // 这里可以根据需要自定义流水号的格式，例如前缀+日期+递增数，下划线分隔
        return type + String.format("%1$tY%1$tm%1$td%1$tk%1$tM%1$tS%2$05d",
                java.util.Calendar.getInstance(), serialNumber.incrementAndGet());
    }

    public static String generateVoc() {
        // 这里可以根据需要自定义流水号的格式，例如前缀+日期+递增数，下划线分隔
        return "VOC" + String.format("%1$tY%1$tm%1$td%1$tk%1$tM%1$tS%2$05d",
                java.util.Calendar.getInstance(), serialNumber.incrementAndGet());
    }

    public static String generate() {
        // 这里可以根据需要自定义流水号的格式，例如前缀+日期+递增数，下划线分隔
        return String.format("%1$tY%1$tm%1$td%1$tk%1$tM%1$tS%2$05d",
                java.util.Calendar.getInstance(), serialNumber.incrementAndGet());
    }
}
package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.BankListResponse;
import com.xinfei.vocmng.itl.rr.UaRequest;
import com.xinfei.vocmng.itl.rr.VoucherGenerateRequest;
import com.xinfei.vocmng.itl.rr.VoucherGenerateResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
@FeignClient(name = FeignConstants.PAY, contextId = FeignConstants.PAY, path = "/")
public interface PayFeignClient {

    @PostMapping("/bank/bank-list")
    BankListResponse bankList(UaRequest ua);

    /**
     * 凭证生成接口
     * @param request 凭证生成请求参数
     * @return 凭证生成响应
     */
    @PostMapping("/api/voucher/generate")
    VoucherGenerateResponse generateVoucher(@RequestBody VoucherGenerateRequest request);

    @PostMapping("/api/voucher/query")
    VoucherGenerateResponse queryVoucher(@RequestBody VoucherGenerateRequest request);


}

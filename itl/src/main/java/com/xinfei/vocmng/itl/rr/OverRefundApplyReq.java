/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import lombok.Data;

/**
 * <AUTHOR>
 * @version $ OfflineRefundApplyReq, v 0.1 2024-05-21 15:56 junjie.yan Exp $
 */
@Data
public class OverRefundApplyReq {

    /**
     * 退款金额，必填
     */
    private Long amount;

    /**
     * 申请人，必填
     */
    private String applyOp;

    /**
     * 申请原因
     */
    private String applyReson;

    /**
     * 银行流水转账渠道,zfb,bank
     */
    private String channelCode;

    /**
     * 退款结果通知tag不需要结果无需传
     */
    private String mqTag;

    /**
     * 收款账号，必填
     */
    private String payeeAccNo;

    /**
     * 收款人名称，必填
     */
    private String payeeName;

    /**
     * 退款银行
     */
    private String refundBankName;

    /**
     * 退款渠道,zfb,bank，必填
     */
    private String refundChannelCode;

    /**
     * 请求唯一流水，必填
     */
    private String reqNo;

    /**
     * 转账流水，必填
     */
    private String transNo;

    /**
     * 来源系统customer:客服系统,repaytrade:还款引擎，必填
     */
    private String ua;
}
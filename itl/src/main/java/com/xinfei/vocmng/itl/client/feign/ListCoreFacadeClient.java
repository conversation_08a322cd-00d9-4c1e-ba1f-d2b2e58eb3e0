package com.xinfei.vocmng.itl.client.feign;

import com.xinfei.listcore.facade.rr.UserListRequest;
import com.xinfei.listcore.facade.rr.dto.UserListDto;
import com.xinfei.vocmng.itl.rr.SmsBlackEditRequest;
import com.xinfei.vocmng.itl.rr.SmsBlackListDetail;
import com.xinfei.vocmng.itl.rr.SmsBlackListRequest;

import java.util.List;


/**
 * <AUTHOR> 2024/10/23 17:11
 * ListCoreFacadeClient
 */


public interface ListCoreFacadeClient {

    /**
     * 营销短信加黑
     */
    Boolean create(UserListDto userListDto);

    /**
     * 营销短信加黑查询
     */
    List<UserListDto> getList(UserListRequest userListDto);


    /**
     * 营销短信加黑修改
     */
    Boolean update(UserListDto userListDto);


    /**
     * 黑名单列表（老系统）
     */
    List<SmsBlackListDetail> smsBlackList(SmsBlackListRequest smsBlackListRequest);


    /**
     * 黑名单修改（老系统）
     */
    Boolean smsBlackEdit(SmsBlackEditRequest smsBlackEditRequest);

}


/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ BaseUserCenterResponse, v 0.1 2023/11/1 16:05 ****.**** Exp $
 */
@Data
public class BaseUserCenterResponse<Response> {
	@JsonProperty("status")
	private Integer status;
	
	@JsonProperty("message")
	private String message;
	
	@JsonProperty("time")
	private Long time;
	
	@JsonProperty("response")
	private Response response;
	
	public boolean isSuccess() {
		return status != null && status.equals(1);
	}
}
/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.ProductFeignClient;
import com.xinfei.vocmng.itl.rr.LoanBaseResponse;
import com.xinfei.vocmng.itl.rr.ProfitProductRequest;
import com.xinfei.vocmng.itl.rr.ProfitProductResponse;
import com.xinfei.vocmng.itl.rr.FinServiceType;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ LcsFeignService, v 0.1 2023-12-23 14:10 junjie.yan Exp $
 */

@Slf4j
@Component
public class ProductFeignService {
    @Resource
    private ProductFeignClient productFeignClient;

    public FinServiceType baseInfo() {
        LoanBaseResponse<FinServiceType> response = null;
        String msg = "ProductFeignClient.baseInfo:";
        try {
            response = productFeignClient.baseInfo("fin", "SERVICE_TYPE_FOR_APP");
            log.info(LogUtil.clientLog("ProductFeignClient", "baseInfo", null, response));
            if (Objects.isNull(response) || !"S".equals(response.getFlag()) || !"0000".equals(response.getCode()) || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ProductFeignClient", "baseInfo", null, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<ProfitProductResponse> loadProfitProductByCondition(List<String> profitCodeList) {
        ProfitProductRequest request = new ProfitProductRequest();
        request.setProfitCodeList(profitCodeList);
        LoanBaseResponse<List<ProfitProductResponse>> response = null;
        String msg = "ProductFeignClient.loadProfitProductByCondition:";
        try {
            response = productFeignClient.loadProfitProductByCondition(request);
            log.info(LogUtil.clientLog("ProductFeignClient", "loadProfitProductByCondition", request, response));
            if (Objects.isNull(response) || !"S".equals(response.getFlag()) || !"0000".equals(response.getCode()) || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ProductFeignClient", "loadProfitProductByCondition", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }
}
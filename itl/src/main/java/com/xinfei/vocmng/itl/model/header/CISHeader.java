package com.xinfei.vocmng.itl.model.header;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * CIS服务公共Header信息
 * <AUTHOR>
 * @since 2023/12/17
 */
@Data
public class CISHeader extends BaseHeader {
    /** 调用链路id */
    @JsonProperty(value = "TRACE_ID")
    private String traceId;
    /** 应用名称 */
    @JsonProperty(value = "upstreamService")
    private String appName;
    /** 请求时间，格式：yyyy-MM-dd HH:mm:ss */
    @JsonProperty(value = "requestTime")
    private String requestTime;
}

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ DownloadUrlResponse, v 0.1 2024/6/4 17:48 wancheng.qu Exp $
 */
@Data
public class DownloadUrlResponse implements Serializable {
    @JsonProperty("download_url")
    private String downloadUrl;

}
/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ ImSessionsDetailsQueryResponse, v 0.1 2024-12-18 16:28 pengming.liu Exp $
 */
@Data
public class ImSessionsDetailsQueryResponse {
    private Integer status;
    private String message;
    private Integer size;
    private Integer total;
    @JsonProperty("total_pages")
    private Integer totalPages;

    @JsonProperty("im_log_infos")
    private List<ImSessionsDetailsResponse> imLogInfos;
}
package com.xinfei.vocmng.itl.rr.acsdatacore;

import com.xinfei.vocmng.itl.rr.ImCallLogResponse;
import com.xinfei.vocmng.itl.rr.ImSessionsDetailsResponse;
import com.xinfei.vocmng.itl.rr.ImSessionsResponse;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ ACSPushData, v 0.1 2025/3/6 17:51 shaohui.chen Exp $
 */
@Data
public class ACSPushData {

    /**
     * 会话
     */
    ImSessionsResponse imSessionsResponse;

    /**
     * 聊天对话
     */
    List<ImSessionsDetailsResponse> imSessionsDetailsResponseList;

    /**
     * 随路数据
     */
    ACSFollowData acsFollowData;

    /**
     * 电话数据
     */
    List<ImCallLogResponse> imCallLogResponseList;

}

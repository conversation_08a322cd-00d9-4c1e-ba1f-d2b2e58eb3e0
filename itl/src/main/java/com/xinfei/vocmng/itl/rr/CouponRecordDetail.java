package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR> 2024/8/13 11:12
 * CouponRecordDetail
 */
@Data
public class CouponRecordDetail {

    /**
     * 优惠券id
     */
    @JsonProperty(value = "coupon_id")
    private String couponId;

    /**
     * 优惠券名称
     */
    @JsonProperty(value = "coupon_name")
    private String couponName;

    /**
     * 券类型 1:借款免息券 2:还款立减金 3:限时提额券 4:拉卡拉聚合支付 5:x天免息券
     */
    @JsonProperty(value = "coupon_type")
    private String couponType;

    /**
     * 0:可使用 1:已使用 2:无效
     */
    private String status;

    /**
     * 使用详情
     */
    @JsonProperty(value = "use_detail")
    private String useDetail;

    /**
     * 使用时间
     */
    @JsonProperty(value = "used_time")
    private String usedTime;

    /**
     * 优惠金额
     */
    @JsonProperty(value = "discount_amount")
    private String discountAmount ;

    /**
     * 减免科目：1 手续费，2 权益费，3通用 ,4:息费 5 免息券
     */
    @JsonProperty(value = "discount_category")
    private String discountCategory  ;

    /**
     * 创建时间
     */
    @JsonProperty(value = "created_time")
    private String createdTime;

}

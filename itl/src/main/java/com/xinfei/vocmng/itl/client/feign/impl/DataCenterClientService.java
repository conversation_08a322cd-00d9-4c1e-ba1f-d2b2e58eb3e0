/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.DataCenterClient;
import com.xinfei.vocmng.itl.rr.MobileGetData;
import com.xinfei.vocmng.itl.rr.MobileGetReq;
import com.xinfei.vocmng.itl.rr.MobileGetResp;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version $ DataCenterClientService, v 0.1 2024-07-31 14:02 junjie.yan Exp $
 */
@Slf4j
@Component
public class DataCenterClientService {

    @Resource
    private DataCenterClient dataCenterClient;

    @Value("${data-center.key}")
    private String dataCenterKey;

    public MobileGetData mobileGet(String mobileProtyle) {
        MobileGetReq req = new MobileGetReq();
        MobileGetResp response = null;
        String msg = "DataCenterClient.mobileGet:";
        try {

            req.setKey(dataCenterKey);
            req.setMobile_protyle(mobileProtyle);
            response = dataCenterClient.mobileGet(UUID.randomUUID().toString(), req);

            log.info(LogUtil.clientLog("DataCenterClient", "mobileGet", req, response));
            if (Objects.isNull(response) || response.getStatus() != 1 || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                log.warn(LogUtil.clientErrorLog("DataCenterClient", "mobileGet", req, response, msg));
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("DataCenterClient", "mobileGet", req, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

}
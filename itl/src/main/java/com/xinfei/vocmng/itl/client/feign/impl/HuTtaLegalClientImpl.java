/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.hutta.common.httplib.WebResponse;
import com.xinfei.huttalegal.facade.DebtTransferFacade;
import com.xinfei.huttalegal.facade.rr.req.LawsuitAgencyQueryReq;
import com.xinfei.huttalegal.facade.rr.res.LegalAgencyDetail;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.HuTtaLegalClient;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> 2024/11/15 12:01
 * HuTtaLegalClientImpl
 */

@Component
@Slf4j
public class HuTtaLegalClientImpl implements HuTtaLegalClient {


    @Resource
    private DebtTransferFacade debtTransferFacade;


    @Override
    public LegalAgencyDetail queryAgencyDetail(LawsuitAgencyQueryReq request) {
        WebResponse<LegalAgencyDetail> response = null;
        String msg = "ManageQueryFacade.orderList:";
        try {
            response = debtTransferFacade.queryAgencyDetail(request);
            log.info(LogUtil.clientLog("DebtTransferFacade", "queryAgencyDetail", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("DebtTransferFacade", "queryAgencyDetail", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }

        return response.getData();
    }

}
/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.xinfei.vocmng.itl.rr.dto.ObjType;
import lombok.Data;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ FeatureQueryResp, v 0.1 2025-02-13 17:23 junjie.yan Exp $
 */
@Data
public class FeatureQueryResp {

    private boolean suc;
    private boolean hasFailed;
    private Map<String, ObjType> featureValues;
    private ErrorContext errorContext;

}
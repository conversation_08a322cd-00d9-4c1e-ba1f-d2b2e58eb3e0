package com.xinfei.vocmng.itl.interceptor;

import feign.Client;
import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign监控配置，用于记录Feign请求和响应的详细信息
 *
 * <AUTHOR>
 * @version $ FeignMonitorConfig, v 0.1 2025/4/9 17:09 shaohui.chen Exp $
 */
@Configuration
public class FeignMonitorConfig {

    @Bean
    public static BeanPostProcessor feignClientMonitorWrapper() {
        return new BeanPostProcessor() {

            @Override
            public Object postProcessAfterInitialization(Object bean, String beanName) {
                // 找到真正的 Feign Client（排除我们自己包过的）
                if (bean instanceof Client && !(bean instanceof MonitorFeignClient)) {
                    return new MonitorFeignClient((Client) bean);
                }
                return bean;
            }
        };
    }

    @Bean
    public RequestInterceptor feignNameInterceptor() {
        return template -> {
            // 接口简单类名，如 CrowdClient、UserClient
            String interfaceName = template.feignTarget().type().getSimpleName();

            template.header("X-Feign-Interface", interfaceName);
        };
    }
}

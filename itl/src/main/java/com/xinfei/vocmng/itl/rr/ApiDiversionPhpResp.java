/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.itl.rr;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ ApiDiversionPhpResp, v 0.1 2024/8/14 19:42 you.zhang Exp $
 */
@Data
public class ApiDiversionPhpResp {

    private Integer status;
    private String message;
    private Long time;
    private ApiResponse response;

    public boolean isSuccess() {
        return FeignConstants.SUCCESS_CODE.equals(this.status);
    }

}

package com.xinfei.vocmng.itl.client.feign;

import com.xyf.cis.dto.SecureEncryptDTO;
import com.xyf.cis.query.facade.dto.standard.response.*;
import com.xyf.user.facade.common.model.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ CIsFacadeClient, v 0.1 2023/11/11 12:52 valiant.shaw Exp $
 * @Description: <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001017392">cis-query 标准查询接口汇总</a>
 */
public interface CisFacadeClient {

    /**
     * @param mobileMd5:
     * @return QueryUserNoByMobilMd5Response
     * <AUTHOR>
     * @description queryUserNoByMobilMd5
     * @date 2025/3/19 16:39
     */
    QueryUserNoByMobilMd5Response queryUserNoByMobilMd5(String mobileMd5);

    /**
     * 分页搜索用户
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001018289">...</a>
     *
     * @param mobileNo 手机号 非必填
     * @param custNo   客户号 非必填
     * @param userNo   userNo 非必填
     * @param pageNum  页数
     * @param pageSize 每页数量
     * @return 用户列表
     */
    PageResult<UserSearchDTO> queryUserList(String mobileNo, String custNo, String userNo, Integer pageNum, Integer pageSize);

    /**
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001017392@toc22">通过手机号获取所有app的用户号</a>
     *
     * @param mobile 必填
     * @return
     */
    List<UserNoDTO> queryUserNoByMobile(String mobile);

    /**
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001017392@toc12">通过userNo获取用户三要素（授信后）</a>
     *
     * @param userNo userNo
     * @return
     */
    ThreeElementsDTO queryThreeElementsByUserNo(Long userNo);

    /**
     * 通过身份证获取custNo
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001017392@toc5">通过身份证获取custNo</a>
     *
     * @param idCardNumber 身份证号 必填
     * @return 客户号相关信息
     */
    CustNoDTO queryCustNoByIdNo(String idCardNumber);

    /**
     * 通过手机号+app获取userNo
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001017392@toc3">通过手机号+app获取userNo</a>
     *
     * @param mobile 手机号 必填
     * @param app    app 必填
     * @return userNo信息
     */
    UserNoDTO getUserNoByMobileAndApp(String mobile, String app);

    /**
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001017392@toc6">通过custNo获取身份证信息</a>
     *
     * @param custNo 必填
     * @return
     */
    IdNoDTO queryIdNoByCustNo(String custNo);

    /**
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001017392@toc16">根据 userNo 查询最近一次登录时间</a>
     *
     * @param userNo 必填
     * @return
     */
    LastLoginDTO queryLastLoginByUserNo(Long userNo);

    /**
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001017392@toc15">根据 userNo 查询custNo(授信完成后的)</a>
     *
     * @param userNo 必填
     * @return
     */
    CustNoDTO queryCustNoByUserNo(Long userNo);

    /**
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001017392@toc4">通过userNo获取手机号+app：</a>
     *
     * @param userNo 必填
     * @return
     */
    MobileDTO queryMobileByUserNo(Long userNo);

    /**
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001018761">五要素加解密对接方案</a>
     * 敏感信息加密：本地加密
     *
     * @param mobiles
     * @return
     */
    List<SecureEncryptDTO> batchEncryptLocal(List<String> mobiles);

    List<SecureEncryptDTO> batchEncryptByField(String field, List<String> plainTexts);

    /**
     * 敏感信息解密：本地解密
     *
     * @param cipherTexts
     * @return
     */
    List<SecureEncryptDTO> batchDecrypt(List<String> cipherTexts, String field);


    /**
     * 通过手机号获取u_user表id
     * https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001017392@toc10
     *
     * @param mobile
     * @return
     */
    Long queryIdByMobile(String mobile);

    /**
     * 通过通过u_user表id获取手机号
     * https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001017392@toc10
     *
     * @param userId
     * @return
     */
    String queryMobileById(Long userId);

}

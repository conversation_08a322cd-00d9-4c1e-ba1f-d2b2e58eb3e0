package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.udesk.UdeskCCBaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version $ CCUdeskClient, v 0.1 2025/7/1 10:52 shaohui.chen Exp $
 */
@FeignClient(name = FeignConstants.CC_DESK, contextId = FeignConstants.CC_DESK, path = "/")
public interface CCUdeskClient {

    /**
     * cc鉴权
     */
    @GetMapping("/ccapi/v2/RESOURCE")
    UdeskCCBaseResponse<Void> promiseSign(
            @RequestParam("AppId") String appId,
            @RequestParam("Timestamp") String timestamp,
            @RequestParam("Sid") String sid,
            @RequestParam("Sign") String sign);

}

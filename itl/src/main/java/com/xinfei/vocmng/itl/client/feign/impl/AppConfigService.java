/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.AppConfigClient;
import com.xinfei.vocmng.itl.rr.AppConfigRequest;
import com.xinfei.vocmng.itl.rr.AppConfigResponse;
import com.xinfei.vocmng.itl.rr.dto.AppConfigDto;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ LcsFeignService, v 0.1 2023-12-23 14:10 junjie.yan Exp $
 */

@Slf4j
@Component
public class AppConfigService {
    @Resource
    private AppConfigClient appConfigClient;

    public List<AppConfigDto> getAllApp(AppConfigRequest request) {

        AppConfigResponse response = null;
        String msg = "AppConfigClient.getAll:";
        try {
            if (Objects.isNull(request)) {
                return null;
            }
            response = appConfigClient.getAll(request);
            log.info(LogUtil.clientLog("AppConfigClient", "getAll", request, response));
            if (Objects.isNull(response) || !"success".equals(response.getMessage()) || response.getStatus() != 1) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            List<String> priorityList = new ArrayList<>();
            priorityList.add("信用飞");
            priorityList.add("飞行卡");
            priorityList.add("畅行花");
            priorityList.add("信用飞irr");
            // 自定义排序
            List<AppConfigDto> list = response.getResponse();
            list.sort(Comparator.comparingInt(app -> {
                // 对 priorityList 也进行相同的处理，确保匹配正确
                int index = priorityList.indexOf(app.getInnerAppName().trim());
                return index == -1 ? Integer.MAX_VALUE : index;
            }));
            list.forEach(appConfigDto -> {
                if ("信用飞irr".equals(appConfigDto.getInnerAppName().trim())) {
                    appConfigDto.setInnerAppName("信用飞01");
                }
                if ("信用飞".equals(appConfigDto.getInnerAppName().trim())) {
                    appConfigDto.setInnerAppName("老信用飞");
                }
            });
            return response.getResponse();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("AppConfigClient", "getAll", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }


}
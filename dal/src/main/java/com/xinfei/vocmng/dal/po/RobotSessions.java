package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("robot_sessions")
public class RobotSessions {
    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 对应接口返回值里的 id */
    @TableField("session_id")
    private Long sessionId;

    /** 机器人id */
    @TableField("robot_id")
    private Long robotId;

    /** 机器人 */
    @TableField("robot")
    private Long robot;

    /** 创建时间 */
    @TableField("start_time")
    private LocalDateTime startTime;

    /** 关闭时间 */
    @TableField("end_time")
    private LocalDateTime endTime;

    /** 对话总数 */
    @TableField("total_count")
    private Long totalCount;

    /** IP值 */
    @TableField("ip_location")
    private String ipLocation;

    /** 客户消息数 */
    @TableField("customer_msg_count")
    private Long customerMsgCount;

    /** 机器人消息数 */
    @TableField("robot_msg_count")
    private Long robotMsgCount;

    /** 机器人匹配回答数 */
    @TableField("robot_replied_count")
    private Long robotRepliedCount;

    /** 未知回答数 */
    @TableField("unknown_question_count")
    private Long unknownQuestionCount;

    /** 有用回答数 */
    @TableField("useful_response_count")
    private Long usefulResponseCount;

    /** 无用回答数 */
    @TableField("useless_response_count")
    private Long uselessResponseCount;

    /** 评价 1.未评价 2.满意 3.一般 4.不满意 */
    @TableField("survey_option")
    private Long surveyOption;

    /** 评价备注 */
    @TableField("survey_comment")
    private String surveyComment;

    /** 是否评价 */
    @TableField("survey_flag")
    private String surveyFlag;

    /** 是否转人工 0.无 1.有 */
    @TableField("is_switch_staff")
    private Long isSwitchStaff;

    /** 应用名称 */
    @TableField("app_name")
    private String appName;

    /** 场景ID */
    @TableField("channel_id")
    private Long channelId;

    /** 场景 */
    @TableField("channel_name")
    private String channelName;

    /** 客户id */
    @TableField("customer_id")
    private Long customerId;

    /** 客户姓名 */
    @TableField("customer_name")
    private String customerName;

    /** imId */
    @TableField("im_sub_session_id")
    private Long imSubSessionId;

    /** 对话id */
    @TableField("call_id")
    private String callId;

    /** 对话描述信息 */
    @TableField("dialogue_desc")
    private String dialogueDesc;

    /** 直接回答数 */
    @TableField("direct_response_count")
    private Long directResponseCount;

    /** 引导选择回答数 */
    @TableField("suggest_question_count")
    private Long suggestQuestionCount;

    /** 引导未选择回答数 */
    @TableField("un_suggest_question_count")
    private Long unSuggestQuestionCount;

    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;

    /** 修改时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /** 客户信息对象 */
    @TableField("customer")
    private String customer;

    /** im */
    @TableField("im")
    private String im;
}
package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 部门表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12 02:21:23
 */
@Data
@TableName("department")
public class Department implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 组织名称 */
    @TableField("name")
    private String name;

    /** 父部门ID */
    @TableField("parent_department_id")
    private Long parentDepartmentId;

    /** 部门级别：0根部门 1一级部门 2二级部门 */
    @TableField("level")
    private Integer level;

    /** 部门负责人,对应身份标识 */
    @TableField("director")
    private String director;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    /** 部门类型（1：内勤 2：外包） */
    @TableField("department_type")
    private Integer departmentType;
}

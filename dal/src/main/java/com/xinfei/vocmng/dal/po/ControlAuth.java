/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ ControlAuth, v 0.1 2024/3/15 17:29 wancheng.qu Exp $
 */
@Data
@TableName("control_auth")
public class ControlAuth {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**'费控场景，1:减免额度，2:抵扣额度，3:退款额度',*/
    @TableField("control_scene")
    private Integer controlScene;

    /** '费控类型，0:无，1:还当期，2:提前结清',*/
    @TableField("control_type")
    private Integer controlType;

    /**'费控子项类型，1:担保费减免额度 ，2:罚息减免额度，3:本金额度，4:利息额度，5:提前结清费，6：抵扣额度，7:抵扣池范围，8:营收订单本金减免范围'*/
    @TableField("control_child_type")
    private Integer controlChildType;

    /** 费控项值类型，1:文本框，2:min~max上下限，3:复选框*/
    @TableField("control_value_type")
    private Integer controlValueType;

    /** 是否需要自定义计算，0不需要，1需要',*/
    @TableField("custom")
    private Integer custom;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField("updated_time")
    private LocalDateTime updatedTime;




}
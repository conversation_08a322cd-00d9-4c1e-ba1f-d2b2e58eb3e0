package com.xinfei.vocmng.dal.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xinfei.vocmng.dal.po.SummaryQuestion;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * admin小结与问题映射表（临时） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10 03:01:33
 */
@DS("union")
public interface SummaryQuestionMapper extends BaseMapper<SummaryQuestion> {

    @Select("SELECT id, summary_id, question_lv1, question_lv2 FROM `summary_question` where summary_id = #{summaryId}")
    SummaryQuestion queryBySummaryIdSummaryQuestion(@Param("summaryId") Long summaryId);

}

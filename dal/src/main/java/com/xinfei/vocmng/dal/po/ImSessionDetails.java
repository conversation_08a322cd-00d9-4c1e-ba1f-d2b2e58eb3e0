package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("im_session_details")
public class ImSessionDetails {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 接口返回值中对应的id */
    @TableField("biz_id")
    private Long bizId;

    /** 所属会话 id */
    @TableField("session_id")
    private Long sessionId;

    /** 所属子会话 id */
    @TableField("sub_session_id")
    private Long subSessionId;

    /** 发送人 id */
    @TableField("user_id")
    private Long userId;

    /** 发送人身份，agent 或 customer */
    @TableField("sender")
    private String sender;

    /** 满意度调查结果 id */
    @TableField("survey_option_id")
    private Long surveyOptionId;

    @TableField("created_at")
    private LocalDateTime createdAt;

    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;

    /** 修改时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /** 消息内容 */
    @TableField("content")
    private String content;
}
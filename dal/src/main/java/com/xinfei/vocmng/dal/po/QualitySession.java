package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22 05:34:43
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("quality_session")
public class QualitySession implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * udesk传递的call_id
     */
    @TableField("call_id")
    private String callId;

    /**
     * 客服员工ID
     */
    @TableField("staff_id")
    private String staffId;

    /**
     * 是否创建会话（0：否，1：是）
     */
    @TableField("is_session")
    private Integer isSession;

    /**
     * 是否提交事件（0：否，1：是）
     */
    @TableField("is_signal")
    private Integer isSignal;

    /**
     * 事件状态
     */
    @TableField("signal_status")
    private String signalStatus;

    /**
     * 会话创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间（提交/关闭事件时间）
     */
    @TableField("last_modified")
    private LocalDateTime lastModified;

    /**
     * 逻辑删除
     */
    @TableField("is_del")
    private Integer isDel;
}

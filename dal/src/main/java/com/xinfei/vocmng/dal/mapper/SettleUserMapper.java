package com.xinfei.vocmng.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xinfei.vocmng.dal.po.EasyComplaintUser;
import com.xinfei.vocmng.dal.po.RiskUser;
import com.xinfei.vocmng.dal.po.SettleUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR> 2024/7/2 16:43
 * 风险用户表操作mapper
 */
@Mapper
public interface SettleUserMapper extends BaseMapper<SettleUser> {

    @Select("select id,mobile_cipher,created_time,updated_time "+
            "from settle_user user where user.mobile_cipher = #{mobile} and is_deleted=0 ")
    SettleUser getSettleUser(@Param("mobile") String mobile);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinfei.vocmng.dal.mapper.IssueCategoryConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinfei.vocmng.dal.po.IssueCategoryConfig">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="parent_id" property="parentId" />
        <result column="level" property="level" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, parent_id, level, is_deleted, created_time, updated_time
    </sql>

    <select id="selectByIds" resultType="com.xinfei.vocmng.dal.po.IssueCategoryConfig">
        SELECT id, name FROM issue_category_config
        <where>
            <!-- 判断 ids 是否非空 -->
            <if test="ids != null and ids.size() > 0">
                AND id IN
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <!-- 当 ids 为空时，添加无效条件使结果为空 -->
            <if test="ids == null or ids.size() == 0">
                AND 1 = 0
            </if>
            <!-- 固定条件：is_deleted=0 -->
            AND is_deleted = 0
        </where>
    </select>

</mapper>

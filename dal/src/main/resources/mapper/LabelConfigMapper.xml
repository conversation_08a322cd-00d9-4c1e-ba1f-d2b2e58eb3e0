<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinfei.vocmng.dal.mapper.LabelConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinfei.vocmng.dal.po.LabelConfig">
        <id column="id" property="id" />
        <result column="category_id" property="categoryId" />
        <result column="name" property="name" />
        <result column="color" property="color" />
        <result column="solution" property="solution" />
        <result column="display_state" property="displayState" />
        <result column="user_identify" property="userIdentify" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, category_id, name, color,solution, display_state, user_identify, is_deleted, created_time, updated_time
    </sql>
</mapper>

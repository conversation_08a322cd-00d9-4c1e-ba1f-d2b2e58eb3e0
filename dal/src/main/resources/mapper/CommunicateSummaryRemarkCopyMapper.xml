<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinfei.vocmng.dal.mapper.CommunicateSummaryRemarkCopyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinfei.vocmng.dal.po.CommunicateSummaryRemarkCopy">
        <id column="id" property="id" />
        <result column="communicate_summary_id" property="communicateSummaryId" />
        <result column="remark" property="remark" />
        <result column="create_user_identify" property="createUserIdentify" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, communicate_summary_id, remark, create_user_identify, is_deleted, created_time, updated_time
    </sql>

</mapper>

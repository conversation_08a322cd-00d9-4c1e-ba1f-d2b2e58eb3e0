<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinfei.vocmng.dal.mapper.CommunicateSummaryCopyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinfei.vocmng.dal.po.CommunicateSummaryCopy">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="issue_category_lv1" property="issueCategoryLv1" />
        <result column="issue_category_lv2" property="issueCategoryLv2" />
        <result column="issue_category_lv3" property="issueCategoryLv3" />
        <result column="source" property="source" />
        <result column="order_no" property="orderNo" />
        <result column="cust_no" property="custNo" />
        <result column="telephone" property="telephone" />
        <result column="call_back_mobile" property="callBackMobile" />
        <result column="create_user_identify" property="createUserIdentify" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="telephone_encrypted" property="telephoneEncrypted" />
        <result column="call_back_mobile_encrypted" property="callBackMobileEncrypted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,telephone_encrypted,call_back_mobile_encrypted, user_id, issue_category_lv1, issue_category_lv2, issue_category_lv3,source, order_no,cust_no, telephone, call_back_mobile, create_user_identify, is_deleted, created_time, updated_time
    </sql>


</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinfei.vocmng.dal.mapper.FeeStrategyConfigMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinfei.vocmng.dal.po.FeeStrategyConfigMapping">
        <id column="id" property="id" />
        <result column="dict_detail_id" property="dictDetailId" />
        <result column="role_id" property="roleId" />
        <result column="strategy_id" property="strategyId" />
        <result column="can_break_out" property="canBreakOut" />
        <result column="need_complaint_channel" property="needComplaintChannel" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="is_del" property="isDel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dict_detail_id, role_id, strategy_id, can_break_out, need_complaint_channel, created_time, updated_time, is_del
    </sql>

</mapper>

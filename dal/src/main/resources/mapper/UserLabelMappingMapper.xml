<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinfei.vocmng.dal.mapper.UserLabelMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinfei.vocmng.dal.po.UserLabelMapping">
        <id column="id" property="id" />
        <result column="mobile" property="mobile" />
        <result column="label_id" property="labelId" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="mobile_encrypted" property="mobileEncrypted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, mobile, label_id, is_deleted, created_time, updated_time,mobile_encrypted
    </sql>

    <select id="getLabelsEncrypted" resultType="com.xinfei.vocmng.dal.po.LabelDto">

        select
            config.id,
            label.label_id,
            label.id as labelMappingId,
            config.name,
            config.solution,
            config.color,
            config.display_state
        from user_label_mapping label
                 join label_config config
                      on label.label_id = config.id
                          and label.mobile_encrypted = #{mobileEncrypted}
                          and label.is_deleted = 0
                          and config.is_deleted = 0
    </select>

    <select id="getLabels" resultType="com.xinfei.vocmng.dal.po.LabelDto">

        select
               config.id,
               label.id as labelMappingId,
               label.mobile,
               label.label_id,
               config.name,
               config.color,
               config.solution,
               config.display_state
        from user_label_mapping label
        join label_config config
        on label.label_id = config.id
        and label.mobile = #{mobile}
        and label.is_deleted = 0
        and config.is_deleted = 0
    </select>

    <select id="getUserLabel" resultType="com.xinfei.vocmng.dal.po.UserLabelMapping">
        select *
        from user_label_mapping
        where mobile = #{mobile}
        and is_deleted = 0
        and label_id in (select id
        from label_config
        where name = '疑似黑产')
    </select>

    <select id="getLabelId" resultType="long">
        select id
        from label_config
        where name = #{name}
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinfei.vocmng.dal.mapper.ControlAuthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinfei.vocmng.dal.po.ControlAuth">
        <id column="id" property="id"/>
        <result column="control_scene" property="controlScene"/>
        <result column="control_type" property="controlType"/>
        <result column="control_child_type" property="controlChildType"/>
        <result column="control_value_type" property="controlValueType"/>
        <result column="custom" property="custom"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, control_scene, control_type, control_child_type, control_value_type, custom, is_deleted, created_time, updated_time
    </sql>



</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinfei.vocmng.dal.mapper.EmployeeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinfei.vocmng.dal.po.Employee">
        <id column="id" property="id" />
        <result column="user_identify" property="userIdentify" />
        <result column="name" property="name" />
        <result column="mobile" property="mobile" />
        <result column="department_id" property="departmentId" />
        <result column="state" property="state" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="mobile_encrypted" property="mobileEncrypted" />
        <result column="sso_user_id" property="ssoUserId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_identify, name, mobile, department_id, state,sso_user_id, is_deleted, created_time, updated_time,mobile_encrypted
    </sql>

    <select id="getUserIdentifyByUserName" resultType="string">
        SELECT user_identify
        FROM employee
        WHERE name in
        <foreach collection="userNames" item="userName" open="(" close=")" separator=",">
            #{userName}
        </foreach>
        and is_deleted=0
    </select>

    <select id="getEmployeeByUserEncodePhones"
            parameterType="java.util.List"
            resultType="com.xinfei.vocmng.dal.po.Employee">
        SELECT
        d.id,
        d.name,
        d.mobile_encrypted AS mobileEncrypted
        FROM employee d
        WHERE 1=1
        <if test="userEncodePhones != null and userEncodePhones.size() > 0">
            AND d.mobile_encrypted IN
            <foreach collection="userEncodePhones" item="encodePhone" open="(" separator="," close=")">
                #{encodePhone}
            </foreach>
        </if>
        AND d.is_deleted = 0
    </select>

    <select id="selectByUserIdentifyIn" resultType="com.xinfei.vocmng.dal.po.Employee">
        SELECT *
        FROM employee
        <where>
            <!-- 判断 userIdentifies 是否非空 -->
            <if test="userIdentifies != null and userIdentifies.size() > 0">
                user_identify IN
                <foreach item="userIdentify" collection="userIdentifies" open="(" separator="," close=")">
                    #{userIdentify}
                </foreach>
            </if>
            <!-- 当 userIdentifies 为空时，添加无效条件使查询结果为空 -->
            <if test="userIdentifies == null or userIdentifies.size() == 0">
                AND 1 = 0
            </if>
            <!-- 固定条件：is_deleted 和 state -->
            AND is_deleted = 0
            AND state = 0
        </where>
    </select>

</mapper>

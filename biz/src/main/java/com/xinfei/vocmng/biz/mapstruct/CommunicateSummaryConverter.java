/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.vocmng.biz.model.enums.IncomingSourceEnum;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryCreateReq;
import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryResp;
import com.xinfei.vocmng.dal.po.CommunicateSummary;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version $ CommunicateSummaryConverter, v 0.1 2024-02-21 11:19 junjie.yan Exp $
 */
@Mapper
public interface CommunicateSummaryConverter {

    CommunicateSummaryConverter INSTANCE = Mappers.getMapper(CommunicateSummaryConverter.class);

    CommunicateSummary reqToCommunicateSummary(CommunicateSummaryCreateReq req);

    @Mapping(target = "issueCategoryLv1", ignore = true)
    @Mapping(target = "issueCategoryLv2", ignore = true)
    @Mapping(target = "issueCategoryLv3", ignore = true)
    @Mapping(source = "source", target = "source", qualifiedByName = "getDescByCode")
    @Mapping(source = "issueCategoryLv1", target = "issueCategoryLv1Id")
    @Mapping(source = "issueCategoryLv2", target = "issueCategoryLv2Id")
    @Mapping(source = "issueCategoryLv3", target = "issueCategoryLv3Id")
    @Mapping(source = "userId", target = "userNo")
    CommunicateSummaryResp communicateSummaryToResp(CommunicateSummary req);


    @Named("getDescByCode")
    static String getDescByCode(Integer code) {
        return IncomingSourceEnum.getDescByCode(code);
    }

}
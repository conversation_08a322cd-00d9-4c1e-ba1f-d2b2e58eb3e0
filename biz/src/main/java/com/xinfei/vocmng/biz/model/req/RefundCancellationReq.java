/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ RefundCancellationReq, v 0.1 2024/3/26 14:35 wancheng.qu Exp $
 */
@Data
public class RefundCancellationReq implements Serializable {

    @ApiModelProperty("退款指令号")
    @NotBlank(message = "退款指令号不能为空")
    private String refundInstructionNo;


}
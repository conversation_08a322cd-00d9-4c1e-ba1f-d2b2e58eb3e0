/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.strategy;

import com.xinfei.vocmng.biz.rr.dto.bill.PlanDetailDto;
import com.xinfei.vocmng.biz.rr.request.FeeAmountDtoResp;
import com.xinfei.vocmng.biz.rr.response.CalculateFee;
import com.xinfei.vocmng.biz.rr.response.ExemptionResponse;
import com.xinfei.vocmng.biz.strategy.impl.GenericExemptionStrategy;
import com.xinfei.vocmng.biz.strategy.impl.SettleFeeExemptionStrategy;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version $ ExemptionStrategyFactory, v 0.1 2025-04-29 14:03 junjie.yan Exp $
 */

public class ExemptionStrategyFactory {
    private static final Map<String, ExemptionApplicationStrategy> strategies = new HashMap<>();

    // 静态初始化块，注册所有策略
    static {
        // 注册通用策略实例，传入字段提取器 和 字段设置器
        strategies.put("Out_Principal", new GenericExemptionStrategy(
                PlanDetailDto::getPrinAmt, PlanDetailDto::getPrinAmt, CalculateFee::getCalPrinAmt,FeeAmountDtoResp::getTransPrin,
                ExemptionResponse::setPrinLower, ExemptionResponse::setPrinUpper, ExemptionResponse::setPrinMax,"prinAmt" // 设置本金字段
        ));
        strategies.put("Out_intAmt", new GenericExemptionStrategy(
                PlanDetailDto::getIntAmt, PlanDetailDto::getIntAmt, CalculateFee::getCalIntAmt,FeeAmountDtoResp::getTransInt,
                ExemptionResponse::setIntLower, ExemptionResponse::setIntUpper, ExemptionResponse::setIntMax,"intAmt" // 设置利息字段
        ));
        strategies.put("Out_Fee1Amt", new GenericExemptionStrategy(
                PlanDetailDto::getFee1Amt, PlanDetailDto::getFee1Amt, CalculateFee::getTransFee1,FeeAmountDtoResp::getTransFee1,
                ExemptionResponse::setFee1Lower, ExemptionResponse::setFee1Upper, ExemptionResponse::setFee1Max,"fee1Amt" // 设置 Fee1 字段
        ));
        strategies.put("Out_Fee2Amt", new GenericExemptionStrategy(
                PlanDetailDto::getFee2Amt, PlanDetailDto::getFee2Amt, CalculateFee::getTransFee2,FeeAmountDtoResp::getTransFee2,
                ExemptionResponse::setFee2Lower, ExemptionResponse::setFee2Upper, ExemptionResponse::setFee2Max,"fee2Amt" // 设置 Fee2 字段
        ));
        strategies.put("Out_Fee6Amt", new GenericExemptionStrategy(
                PlanDetailDto::getFee6Amt, PlanDetailDto::getFee6Amt, CalculateFee::getTransFee6,FeeAmountDtoResp::getTransFee6,
                ExemptionResponse::setFee6Lower, ExemptionResponse::setFee6Upper, ExemptionResponse::setFee6Max,"fee6Amt" // 设置 Fee6 字段
        ));
        strategies.put("Out_OintAmt", new GenericExemptionStrategy(
                PlanDetailDto::getOintAmt, PlanDetailDto::getOintAmt, CalculateFee::getTransOint,FeeAmountDtoResp::getTransOint,
                ExemptionResponse::setOintLower, ExemptionResponse::setOintUpper, ExemptionResponse::setOintMax,"ointAmt" // 设置 Oint 字段
        ));
        strategies.put("Out_Fee3Amt", new GenericExemptionStrategy(
                PlanDetailDto::getFee3Amt, PlanDetailDto::getFee3Amt, CalculateFee::getTransFee3,FeeAmountDtoResp::getTransFee3,
                ExemptionResponse::setFee3Lower, ExemptionResponse::setFee3Upper, ExemptionResponse::setFee3Max,"fee3Amt" // 设置 Fee3 字段
        ));

        // 注册特殊的策略实例 (它内部知道要设置哪个字段，或者也可以让它接收 setter)
        strategies.put("Out_Settle_Fee", new SettleFeeExemptionStrategy());

    }

    /**
     * 根据 key 获取计算并应用 Exemption 的策略
     *
     * @param key 结果 Map 中的 key
     * @return 对应的 ExemptionApplicationStrategy，如果找不到则返回 Optional.empty()
     */
    public static Optional<ExemptionApplicationStrategy> getStrategy(String key) {
        return Optional.ofNullable(strategies.get(key));
    }

}
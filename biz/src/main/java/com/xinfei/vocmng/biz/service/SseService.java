/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.component.SlidingWindowRateLimiter;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * <AUTHOR>
 * @version $ SseService, v 0.1 2024/2/6 19:10 wancheng.qu Exp $
 */
@Service
@Slf4j
public class SseService {

    @Resource
    private SlidingWindowRateLimiter rateLimiter;

    private final Map<String, SseEmitter> emitters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, ReadWriteLock> lockMap = new ConcurrentHashMap<>();

    private ReadWriteLock getLock(String userIdentify) {
        return lockMap.computeIfAbsent(userIdentify, k -> new ReentrantReadWriteLock());
    }

    public void addEmitter(String userIdentify, SseEmitter emitter) {
        ReadWriteLock lock = getLock(userIdentify);
        lock.writeLock().lock();
        try {
            SseEmitter previousEmitter = emitters.put(userIdentify, emitter);
            if (previousEmitter != null) {
                previousEmitter.complete();
            }
        } catch (Exception e) {
            log.info("addEmitter error,userIdentify={},err={}", userIdentify, e.getMessage());
        } finally {
            lock.writeLock().unlock();
        }
    }


    /**
     * @param null:
     * @return null
     * <AUTHOR>
     * @description 当连接更新时，阻塞连接的获取，保证获取到最新的连接，锁粒度为单个用户;消息维度通过滑动窗口去重
     * @date 2024/3/13 17:51
     */
    public void sendEventToUser(String userIdentify, Map<String, Object> event) {
        String mesgId=getMesgId(event);
        boolean acquired = rateLimiter.tryAcquire(mesgId);
        if(!acquired){
            log.info("could not acquire the rate limiter,userIdentify={},mesgid:{}",userIdentify,mesgId);
            return;
        }
        ReadWriteLock lock = getLock(userIdentify);
        lock.readLock().lock();
        try {
            SseEmitter emitter = emitters.get(userIdentify);
            if (emitter != null) {
                log.info("sse sendEventToUser userIdentify={},mesgId={}", userIdentify, mesgId);
                try {
                    emitter.send(event, MediaType.APPLICATION_JSON);
                } catch (Exception e) {
                    log.warn("sse send warn, userIdentify={}, mesgId={}, err={}", userIdentify, mesgId, e.getMessage());
                    emitter.completeWithError(e);
                }
            } else {
                log.info("sse sendEventToUser emitter is null, userIdentify={}, mesgId={}", userIdentify,mesgId);
            }
        } finally {
            lock.readLock().unlock();
        }
    }

    public  String getMesgId(Map<String, Object> event) {
        return Optional.ofNullable(event)
                .map(e -> e.getOrDefault("mesgId", null))
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .orElse(null);
    }



}

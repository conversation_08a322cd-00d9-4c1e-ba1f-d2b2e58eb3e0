/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.config;

import com.xinfei.vocmng.biz.model.event.QualitySseEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ CustomEventPublisher, v 0.1 2024/2/7 15:41 wancheng.qu Exp $
 */

@Component
public class QualityEventPublisher {

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    public void publishEvent(String userIdentify, String customerPhone, String displayNumber, String callId, String status) {
        QualitySseEvent event = new QualitySseEvent(this, userIdentify, customerPhone, displayNumber, callId, status);
        applicationEventPublisher.publishEvent(event);
    }
}

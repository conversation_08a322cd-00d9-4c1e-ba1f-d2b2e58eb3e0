/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员卡支付明细详情DTO
 *
 * <AUTHOR>
 * @version $ VipOrderPayLogDetailDTO, v 0.1 2025/5/09 16:30 shaohui.chen Exp $
 */
@Data
@ApiModel(description = "会员卡支付明细详情")
public class VipOrderPayLogDetailDTO {
    @ApiModelProperty("支付时间")
    private LocalDateTime payTime;
    @ApiModelProperty("支付金额")
    private BigDecimal payAmount;
    @ApiModelProperty("支付结果，pay_start:待支付、paying:支付中、pay_fail:支付失败、pay_success:支付成功、pay_cancel:支付取消、pay_close:支付关闭")
    private String payStatus;
    @ApiModelProperty("支付结果描述")
    private String payStatusDesc;
    @ApiModelProperty("支付人")
    private String payUserNo;
    @ApiModelProperty("支付类型，wechat:微信、alipay:支付宝、debit_card:储蓄卡")
    private String payType;
    @ApiModelProperty("支付类型描述")
    private String payTypeDesc;
    @ApiModelProperty("支付账号")
    private String payAccount;
}

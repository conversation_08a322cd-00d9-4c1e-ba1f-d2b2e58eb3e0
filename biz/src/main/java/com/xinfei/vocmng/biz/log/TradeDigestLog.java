/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.log;

import cn.hutool.json.JSONObject;
import com.xinfei.vocmng.biz.rr.UserRequest;
import lombok.Setter;

/**
 * 交易类日志摘要
 *
 * <AUTHOR>
 * @version $ QueryDigestLog, v 0.1 2023/8/28 20:32 <PERSON>yan.Huang Exp $
 */
@Setter
final public class TradeDigestLog extends BaseDigestLog {

    /**
     * 用户名
     */
    private String userName;

    /**
     * 应用名
     */
    private String appName;

    /**
     * 构造器
     *
     * @param userRequest 用户请求
     */
    public TradeDigestLog(UserRequest userRequest) {
        if (userRequest == null || userRequest.getUser() == null) {
            return;
        }
        this.userName = userRequest.getUser().getName();
    }

    /**
     * 构造器
     *
     * @param appName
     */
    public TradeDigestLog(String appName) {
        if (appName == null) {
            return;
        }
        this.appName = appName;
    }

    /**
     * 日志业务信息组装
     *
     * @param buffer 日志信息
     */
    protected void composeBizInfo(StringBuilder buffer) {
        buffer.append("(");
        JSONObject json = new JSONObject();
        {
            json.putOnce("userName", userName);
            json.putOnce("appName", appName);
        }
        buffer.append(json.toJSONString(0));
        buffer.append(")");
    }

}

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.model.base.ControlItemValue;
import com.xinfei.vocmng.biz.model.base.ControlRes;
import com.xinfei.vocmng.biz.service.CostCalculationStrategy;
import com.xinfei.vocmng.biz.util.ReductionStrategyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR> 2024/7/17 下午3:17
 * 新抵扣范围算法
 */
@Service
@Slf4j
public class DeductionAmtStrategy implements CostCalculationStrategy<BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal> {

    @Override
    public ControlRes<BigDecimal, BigDecimal> calculateAmount(ControlItemValue<BigDecimal, BigDecimal, BigDecimal> values, Object... params) {
        //应还金额
        BigDecimal calTotalAmt = BigDecimal.ZERO;
        //借据初始总金额
        BigDecimal sumAmt = BigDecimal.ZERO;
        //借据历史抵扣金额
        BigDecimal reductionTotalAmount = BigDecimal.ZERO;

        for (int i = 0; i < params.length; i++) {
            if (params[i] instanceof BigDecimal) {
                if (i == 0) {
                    calTotalAmt = ((BigDecimal) params[i]);
                }
                if (i == 1) {
                    sumAmt = ((BigDecimal) params[i]);
                }
                if (i == 2) {
                    reductionTotalAmount = ((BigDecimal) params[i]);
                }
            }
        }
        return ReductionStrategyUtil.calculateAmount(values.getT(), values.getO(), sumAmt, reductionTotalAmount, calTotalAmt);
    }
}
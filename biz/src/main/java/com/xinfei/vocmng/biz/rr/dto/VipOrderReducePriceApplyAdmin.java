/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ VipOrderReducePriceApplyAdmin, v 0.1 2025-06-26 14:12 junjie.yan Exp $
 */
@Data
public class VipOrderReducePriceApplyAdmin {
    @ApiModelProperty("减免申请ID")
    private Long id;
    @ApiModelProperty("减免申请流水号")
    private String applyFlowNo;
    @ApiModelProperty("会员订单号")
    private String vipOrderNo;
    @ApiModelProperty("用户号")
    private String userNo;
    @ApiModelProperty("客户号")
    private String custNo;
    @ApiModelProperty("减免金额(元)")
    private BigDecimal reduceAmount;
    @ApiModelProperty("生效中:enable, 已使用:used, 已撤回: canceled")
    private String applyStatus;
    @ApiModelProperty("方案状态")
    private Integer planStatus;
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
    @ApiModelProperty("有效方案来源(vocmng:客服 HUTTA:催收)")
    private String planSource;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("创建人")
    private String creator;
    @ApiModelProperty("更新人")
    private String updater;
    @ApiModelProperty("方案类型")
    private String solutionType;

}
/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import com.xinfei.vocmng.biz.model.enums.RepayProcessEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ RepaymentProcessReq, v 0.1 2024-03-29 17:18 junjie.yan Exp $
 */
@Data
public class RepaymentProcessReq {

    @ApiModelProperty("借据号")
    @NotEmpty
    private List<String> loanNos;

    @ApiModelProperty("借据号订单号映射")
    @NotEmpty
    private Map<String, String> loanOrderNos;

    @ApiModelProperty("借据号抵扣金额映射")
    private Map<String, BigDecimal> loanNoDeductions;

    @ApiModelProperty("借据号与期数映射")
    private Map<String, Integer> loanNoTerms;

    @ApiModelProperty("还款类型（1：非结清，2：结清）")
    @NotNull
    private Integer repayType;

    @ApiModelProperty("一级投诉渠道")
    private String complaintChannelLv1;

    @ApiModelProperty("二级投诉渠道")
    private String complaintChannelLv2;

    @ApiModelProperty(value = "还款流程")
    @NotEmpty
    private List<RepayProcessEnum> repayProcessEnums;
}
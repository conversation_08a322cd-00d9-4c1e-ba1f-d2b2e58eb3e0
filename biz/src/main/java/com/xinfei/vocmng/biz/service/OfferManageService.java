package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.rr.dto.FeeRateViewDto;
import com.xinfei.vocmng.biz.rr.dto.LoanListDto;
import com.xinfei.vocmng.biz.rr.dto.OfferLockedRateRecordDto;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.OfferInvalidResponse;
import com.xinfei.vocmng.biz.rr.response.Paging;

import java.util.List;

/**
 * Offer管理平台Service
 *
 * <AUTHOR>
 * @version $ OfferManageService, v 0.1 2025-07-21 shaohui.chen Exp $
 */
public interface OfferManageService {

    /**
     * 查询借据列表
     *
     * @param request 查询请求
     * @return 借据列表
     */
    List<LoanListDto> queryLoanList(LoanListRequest request);

    /**
     * 检查未来期本金是否小于减免金额
     *
     * @param request 检查请求
     * @return true-未来期本金小于减免金额，false-未来期本金大于等于减免金额
     */
    Boolean checkFuturePrincipalLessThanDeduction(FuturePrincipalCheckRequest request);

    /**
     * 确认费率，建立减免方案并生成固定费率减免
     *
     * @param request 确认费率请求
     * @return 方案ID
     */
    String confirmFeeRate(ConfirmFeeRateRequest request);

    /**
     * 费率查看接口
     *
     * @param request 费率查看请求
     * @return 费率查看结果列表
     */
    List<FeeRateViewDto> queryFeeRateList(FeeRateViewRequest request);

    OfferInvalidResponse feeRateInvalid(String planDetailId);

    Paging<OfferLockedRateRecordDto> getFeeRateRecords(FeeRateRecordReq req);
}

package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.vocmng.biz.api.OrderRemarkApi;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.OrderRemarkDto;
import com.xinfei.vocmng.biz.rr.request.AddOrderRemarkRequest;
import com.xinfei.vocmng.biz.rr.request.QueryOrderRemarkRequest;
import com.xinfei.vocmng.biz.service.OrderRemarkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ OrderRemarkController, v 0.1 2024/1/13 14:38 qu.lu Exp $
 */
@Validated
@LoginRequired
@RestController
public class OrderRemarkController implements OrderRemarkApi {

    @Autowired
    private OrderRemarkService orderRemarkService;

    @Override
    public ApiResponse<Void> addOrderRemark(AddOrderRemarkRequest request) {
        orderRemarkService.addOrderRemark(request);
        return ApiResponse.success(null);
    }

    @Override
    public ApiResponse<List<OrderRemarkDto>> queryOrderRemark(QueryOrderRemarkRequest request) {
        return ApiResponse.success(orderRemarkService.queryOrderRemark(request));
    }
}

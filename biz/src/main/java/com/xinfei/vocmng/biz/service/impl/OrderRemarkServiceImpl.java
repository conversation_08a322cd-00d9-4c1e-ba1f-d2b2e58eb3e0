package com.xinfei.vocmng.biz.service.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xinfei.vocmng.biz.rr.dto.OrderRemarkDto;
import com.xinfei.vocmng.biz.rr.request.AddOrderRemarkRequest;
import com.xinfei.vocmng.biz.rr.request.QueryOrderRemarkRequest;
import com.xinfei.vocmng.biz.service.EmployeeService;
import com.xinfei.vocmng.biz.service.OrderRemarkService;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.po.Employee;
import com.xinfei.vocmng.dal.po.OrderRemark;
import com.xinfei.vocmng.dal.repository.OrderRemarkRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ OrderRemarkServiceImpl, v 0.1 2024/1/13 11:45 qu.lu Exp $
 */
@Slf4j
@Service
public class OrderRemarkServiceImpl implements OrderRemarkService {

    @Autowired
    private OrderRemarkRepo orderRemarkRepo;

    @Autowired
    private EmployeeService employeeService;

    @Override
    public boolean addOrderRemark(AddOrderRemarkRequest request) {
        OrderRemark orderRemark = buildOrderRemark(request);
        return orderRemarkRepo.addOrderRemark(orderRemark);
    }

    @Override
    public List<OrderRemarkDto> queryOrderRemark(QueryOrderRemarkRequest request) {
        List<OrderRemark> remarkList = orderRemarkRepo.queryOrderRemark(request.getOrderNo());
        return convertOrderRemark(remarkList);
    }

    private OrderRemark buildOrderRemark(AddOrderRemarkRequest request){
        OrderRemark remark = new OrderRemark();
        remark.setRemark(request.getRemark());
        remark.setOrderNo(request.getOrderNo());
        remark.setUserIdentify(UserContextHolder.getUserContext().getUserIdentify());
        remark.setCreatedTime(LocalDateTime.now());

        return remark;
    }

    private List<OrderRemarkDto> convertOrderRemark(List<OrderRemark> remarkList){
        if(CollectionUtils.isEmpty(remarkList)){
            return Collections.emptyList();
        }

        List<String> userIdentityList = remarkList.stream().map(OrderRemark::getUserIdentify).collect(Collectors.toList());
        Map<String,Employee> employeeMap = queryEmployee(userIdentityList);


        List<OrderRemarkDto> result = Lists.newArrayListWithCapacity(remarkList.size());
        OrderRemarkDto dto;
        for (OrderRemark remark : remarkList){
            dto = new OrderRemarkDto();
            dto.setRemark(remark.getRemark());
            dto.setCreateTime(DateUtil.formatLocalDateTime(remark.getCreatedTime()));
            Employee employee = employeeMap.get(remark.getUserIdentify());
            if(employee != null){
                dto.setName(employee.getName());
                dto.setUserCode(String.valueOf(employee.getId()));
            }

            result.add(dto);
        }

        return result;
    }


    private Map<String, Employee> queryEmployee(List<String> identityList){
        if(CollectionUtils.isEmpty(identityList)){
            return Collections.emptyMap();
        }

        List<Employee> employeeList = employeeService.queryEffectiveUser(identityList);
        if(CollectionUtils.isEmpty(employeeList)){
            return Collections.emptyMap();
        }

        Map<String,Employee> result = Maps.newHashMapWithExpectedSize(identityList.size());
        for (Employee employee : employeeList){
            result.put(employee.getUserIdentify(),employee);
        }

        return result;
    }
}

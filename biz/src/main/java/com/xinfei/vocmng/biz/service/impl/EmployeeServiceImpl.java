/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.service.EmployeeRoleMappingService;
import com.xinfei.vocmng.biz.service.EmployeeService;
import com.xinfei.vocmng.dal.mapper.DepartmentMapper;
import com.xinfei.vocmng.dal.mapper.EmployeeMapper;
import com.xinfei.vocmng.dal.mapper.UserLabelMappingMapper;
import com.xinfei.vocmng.dal.po.Employee;
import com.xinfei.vocmng.dal.po.EmployeeRoleMapping;
import com.xinfei.vocmng.dal.po.UserLabelMapping;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ EmployeeServiceImpl, v 0.1 2023/12/23 20:05 wancheng.qu Exp $
 */
@Slf4j
@Service
public class EmployeeServiceImpl extends BaseService<EmployeeMapper, Employee> implements EmployeeService {

    @Resource
    private EmployeeMapper employeeMapper;
    @Resource
    private EmployeeRoleMappingService employeeRoleMappingService;
    @Resource
    private UserLabelMappingMapper userLabelMappingMapper;
    @Resource
    private CisFacadeClientService cisFacadeClientService;
    @Resource
    private VocConfig vocConfig;
    @Resource
    private DepartmentMapper departmentMapper;

    @Override
    public List<Employee> queryEffectiveUser(List<String> identifyList) {
        LambdaQueryWrapper<Employee> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Employee::getUserIdentify,identifyList);
        wrapper.orderByDesc(Employee::getCreatedTime);

        return employeeMapper.selectList(wrapper);
    }

    @Override
    @Transactional
    public void updateSsoUserId(String identify, Long ssoUserId) {
        LambdaUpdateWrapper<Employee> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Employee::getUserIdentify, identify);
        Employee e = new Employee();
        e.setSsoUserId(ssoUserId);
        employeeMapper.update(e, updateWrapper);
    }

    @Override
    public Employee getEffectiveUser(Employee condition) {
        QueryWrapper<Employee> queryWrapper = Wrappers.query();
        queryWrapper.eq("is_deleted", 0);
        queryWrapper.setEntity(condition);
        queryWrapper.last("limit 1");
        return getOne(queryWrapper, true);
    }

    @Override
    public String getUserNameForIdentify(String identify) {
        Employee e = new Employee();
        e.setUserIdentify(identify);
        Employee es = getEffectiveUser(e);
        if (Objects.nonNull(es)) {
            return es.getName();
        }
        return null;
    }

    @Override
    public List<String> findNotExistingRoleNames(List<String> roles) {
        List<String> a = employeeMapper.findNotExistingRoleNames(roles);
        return roles.stream()
                .filter(name -> !a.contains(name))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findNotExistingDepartmentNames(List<String> departs) {
        List<String> b = employeeMapper.findNotExistingDepartNames(departs);
        return departs.stream()
                .filter(name -> !b.contains(name))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findNotExistingMobileNames(List<String> mobile) {

        Map<String, String> m = cisFacadeClientService.getEncodeMobileMap(mobile);
        QueryWrapper<Employee> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("mobile_encrypted")
                .in("mobile_encrypted", m.keySet())
                .eq("is_deleted", 0);

        List<String> mList = employeeMapper.selectObjs(queryWrapper).stream()
                .map(obj -> (String) obj)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mList)) {
            return mList.stream().map(t -> m.get(t)).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    @Transactional
    public void saveData(List<MutablePair<List<EmployeeRoleMapping>, List<Employee>>> dataList) {
        try {
            for (MutablePair<List<EmployeeRoleMapping>, List<Employee>> pair : dataList) {
                List<EmployeeRoleMapping> mappingBatch = pair.getLeft();
                List<Employee> employeeBatch = pair.getRight();
                saveBatch(employeeBatch);
                employeeRoleMappingService.saveBaths(mappingBatch);
            }
        } catch (Exception e) {
            log.error("employeeRoleMappingService saveData error",e);
            throw new TechplayException(TechplayErrDtlEnum.IMP_DATA_ERROR, "数据插入异常");
        }

    }

    @Override
    public List<String> findNotExistingLabelType(List<String> strings) {
        List<String> b = employeeMapper.findNotExistingLabelType(strings);
        return strings.stream()
                .filter(name -> !b.contains(name))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findNotExistingLabelName(List<String> strings) {
        List<String> b = employeeMapper.findNotExistingLabelName(strings);
        return strings.stream()
                .filter(name -> !b.contains(name))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void saveLabelEmp(List<List<UserLabelMapping>> res) {
        for(List<UserLabelMapping> l:res){
            for (UserLabelMapping u:l){
                try {
                    userLabelMappingMapper.insert(u);
                }catch (Exception e){
                    log.warn("saveLabelEmp data is repeat",e);
                }
            }
        }
    }

    @Override
    public String getUserDepartment(String identify) {
        Employee e = new Employee();
        e.setUserIdentify(identify);
        Employee es = getEffectiveUser(e);
        if (Objects.nonNull(es)) {
            return departmentMapper.getNameById(es.getDepartmentId());
        }
        return null;
    }
}
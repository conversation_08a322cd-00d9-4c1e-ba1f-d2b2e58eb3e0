/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import com.xinfei.vocmng.biz.model.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ RepayPlanListReq, v 0.1 2024-03-28 21:42 junjie.yan Exp $
 */
@Data
public class RepayPlanListRequest extends PageRequestDto {

    @ApiModelProperty("方案编号")
    private Long planId;

    @ApiModelProperty("custNo")
    private String custNo;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("方案状态:0：待生效、1：生效中 、2：失效、3：成功")
    private Integer planStatus;

    @ApiModelProperty("审批状态:0：待审批、1：审批通过 、3：审批驳回")
    private Integer reviewStatus;

    @ApiModelProperty("是否审批")
    private Boolean isReview;

    @ApiModelProperty(value = "userNo")
    private String userNo;

    @ApiModelProperty("创建时间起始")
    private LocalDateTime createdTimeStart;

    @ApiModelProperty("创建时间结束")
    private LocalDateTime createdTimeEnd;

}
package com.xinfei.vocmng.biz.rr.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员卡列表信息
 *
 * <AUTHOR>
 * @version $ MemberCardListDto, v 0.1 2023/12/21 20:36 qu.lu Exp $
 */
@Data
public class MemberCardDto {

    //所有卡（老卡、新卡、飞享会员、飞跃会员）
    @ApiModelProperty("会员卡ID")
    private Integer cardId;

    @ApiModelProperty("会员卡类型：1老会员卡  2新会员卡 3飞享会员 4飞跃会员")
    private Integer type;

    @ApiModelProperty("APP用户ID")
    private String userId;

    @ApiModelProperty("用户名称")
    @DataPermission(type = DataPermissionType.MASK_NAME)
    private String name;

    @ApiModelProperty("手机号码")
    @DataPermission(type = DataPermissionType.MASK_TELE)
    private String mobile;

    @ApiModelProperty("app")
    private String app;

    @ApiModelProperty("会员卡名称")
    private String cardName;

    @ApiModelProperty("支付类型：1:微信，2:支付宝，3:银行卡")
    private Integer payType;

    @ApiModelProperty("支付类型描述")
    private String payTypeDesc;

    @ApiModelProperty("支付金额:元")
    private BigDecimal payAmount;

    @ApiModelProperty("生效时间")
    private String beginTime;

    @ApiModelProperty("过期时间")
    private String endTime;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("支付订单号")
    private String orderNo;

    //老卡、新卡
    @ApiModelProperty("支付状态（老卡、新卡）")
    private Integer payStatus;

    @ApiModelProperty("支付状态描述（老卡、新卡）")
    private String payStatusDesc;

    @ApiModelProperty("卡状态：1开卡，2退卡 ，3失效 （老卡、新卡）")
    private Integer cardStatus;

    @ApiModelProperty("卡状态描述 （老卡、新卡）")
    private String cardStatusDesc;


    //飞享会员、飞跃会员
    @ApiModelProperty("飞享/飞跃会员状态")
    private String vipStatus;

    @ApiModelProperty("支付时间")
    private String payTime;

    @ApiModelProperty("会员类型 1：月卡，2:连续包月，3:季卡 4:连续包季")
    private Integer vipTerm;

    @ApiModelProperty("会员订单号")
    private String orderNumber;

    @ApiModelProperty("订单操作类型：1:首次购买，2:手动续期，3-自动续期，4-手动关闭，5-自动关闭")
    private Integer orderBuyType;

    @ApiModelProperty("签章BizNo")
    private String contractBizNo;

    //飞跃会员
    @ApiModelProperty("划线价（元）")
    private BigDecimal originalPrice;
    @ApiModelProperty("签约价（元）")
    private BigDecimal contractPrice;
    @ApiModelProperty("真实售价（元）")
    private BigDecimal salePrice;

    @ApiModelProperty("扣款类型，active_pay:主动支付、withhold:免密代扣（批扣）")
    private String deductType;

    @ApiModelProperty("能否减免(true: 可以减免 false: 不能减免)")
    private Boolean canReduce;

    @ApiModelProperty("是否存在生效中方案(true: 是 false: 否)")
    private Boolean hasReducePlan;

    @ApiModelProperty("减免金额(元)")
    private BigDecimal reduceAmount;

    @ApiModelProperty("减免申请ID")
    private Long planId;

    //特殊
    @ApiModelProperty("提额金额, 单位分(新卡)")
    private BigDecimal limitUpAmount;

    @ApiModelProperty("借款单号")
    private String lendNo;

    @ApiModelProperty("提额卡订单状态")
    private String orderStatus;

    //前端不使用
    @ApiModelProperty("退款状态")
    @Deprecated
    private String refundStatus;

    @ApiModelProperty("操作人")
    @Deprecated
    private String operatorName;

    //飞享会员
    @ApiModelProperty("订单状态：0:初始未支付，1:处理中，2:支付发起，3-支付成功，4-支付失败，5-支付关闭")
    @Deprecated
    private Integer status;

    @ApiModelProperty("会员卡配置id")
    @Deprecated
    private Integer vipId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime dateCreated;

    @ApiModelProperty(value = "借据状态:RP-正常, OD-逾期, FP-结清——(LCS)")
    private String loanStatus;

    @ApiModelProperty(value = "借款金额")
    private BigDecimal loanAmount;
}

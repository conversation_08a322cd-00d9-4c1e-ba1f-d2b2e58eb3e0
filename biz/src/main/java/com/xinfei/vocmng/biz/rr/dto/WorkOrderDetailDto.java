package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 工单明细信息
 *
 * <AUTHOR>
 * @version $ WorkOrderDetail, v 0.1 2024/1/15 15:37 qu.lu Exp $
 */
@Data
public class WorkOrderDetailDto {
    @ApiModelProperty("工单主键ID")
    private Integer id;
    @ApiModelProperty("工单编号")
    private String workOrderNo;
    @ApiModelProperty("工单类型一级描述")
    private String workOrderTypeLv1;
    @ApiModelProperty("工单类型二级描述")
    private String workOrderTypeLv2;
    @ApiModelProperty("工单类型三级描述")
    private String workOrderTypeLv3;
    @ApiModelProperty("工单状态，0未提交分配，1待分配，2跟进中，3转派，4退单，5终止， 6知悉结案，7催收结案，8已结案，9失联结案，10不接受方案")
    private String status;
    @ApiModelProperty("创建时间")
    private String createdTime;
    @ApiModelProperty("渠道")
    private String source;
    @ApiModelProperty("工单内容")
    private String comment;
    @ApiModelProperty("创建人")
    private String createUser;
    @ApiModelProperty("处理人")
    private String processUser;
    @ApiModelProperty("最近一次记录")
    private String lastRecord;
}

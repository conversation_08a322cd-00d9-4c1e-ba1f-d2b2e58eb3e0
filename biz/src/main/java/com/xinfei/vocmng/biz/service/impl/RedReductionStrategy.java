/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.model.base.ControlItemValue;
import com.xinfei.vocmng.biz.model.base.ControlRes;
import com.xinfei.vocmng.biz.service.CostCalculationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @version $ PenaltyReductionStrategy, v 0.1 2024/3/27 18:37 wancheng.qu Exp $
 */
@Service
@Slf4j
public class RedReductionStrategy implements CostCalculationStrategy<BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal> {

    @Override
    public ControlRes<BigDecimal, BigDecimal> calculateAmount(ControlItemValue<BigDecimal, BigDecimal, BigDecimal> values, Object... params) {

        BigDecimal o = values.getO();
        BigDecimal t = values.getT();

        BigDecimal init = BigDecimal.ZERO;
        BigDecimal exempt = BigDecimal.ZERO;

        for (int i = 0; i < params.length; i++) {
            if (params[i] instanceof BigDecimal) {
                if (i == 0) {
                    init = ((BigDecimal) params[i]);
                }

                if (i == 1) {
                    exempt = ((BigDecimal) params[i]);
                }
            }
        }
        log.info("calculateAmount process红线挡板:试算金额:" + init + "历史减免:" + exempt + "上限百分比：" + o + "下限百分比：" + t);

        ControlRes<BigDecimal, BigDecimal> cr = new ControlRes<>();

        BigDecimal lower = init.multiply(t).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal upper = init.multiply(o).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
        upper = upper.subtract(exempt);

        if (lower.compareTo(upper) > 0) {
            cr.setLeft(new BigDecimal(0));
            cr.setRight(new BigDecimal(0));
            return cr;
        }

        cr.setLeft(lower);
        cr.setRight(upper);

        return cr;
    }
}
/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.service.ScreenshotConfineUserService;
import com.xinfei.vocmng.dal.mapper.ScreenshotConfineUserMapper;
import com.xinfei.vocmng.dal.mapper.ScreenshotWhiteUserMapper;
import com.xinfei.vocmng.dal.po.ScreenshotConfineUser;
import com.xinfei.vocmng.dal.po.ScreenshotWhiteUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> 2024/7/4 17:24
 * RiskUserServiceImpl
 */
@Slf4j
@Service
public class ScreenshotConfineUserServiceImpl implements ScreenshotConfineUserService {

    @Resource
    private ScreenshotConfineUserMapper screenshotConfineUserMapper;

    @Resource
    private ScreenshotWhiteUserMapper screenshotWhiteUserMapper;

    @Override
    public Boolean getScreenshotConfine(Long userNo) {
        ScreenshotConfineUser screenshotConfineUser = screenshotConfineUserMapper.getScreenshotConfine(String.valueOf(userNo));
        if (screenshotConfineUser != null){
                ScreenshotWhiteUser screenshotWhiteUser = screenshotWhiteUserMapper.getScreenshotWhiteUser(userNo);
            if(screenshotWhiteUser!=null){
                return false;
            }
            return true;
        }
        return false;
    }
}
/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ ExemptionResponse, v 0.1 2024-03-27 20:04 junjie.yan Exp $
 */
@Data
public class ExemptionResponse {

    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("期数")
    private List<Integer> terms;

    @ApiModelProperty("账单号")
    private String billNos;

    @ApiModelProperty("本金减免下限")
    private BigDecimal prinLower;
    @ApiModelProperty("本金减免上限")
    private BigDecimal prinUpper;
    @ApiModelProperty("本金减免最大值")
    private BigDecimal prinMax;

    @ApiModelProperty("利息减免下限")
    private BigDecimal intLower;
    @ApiModelProperty("利息减免上限")
    private BigDecimal intUpper;
    @ApiModelProperty("利息减免最大值")
    private BigDecimal intMax;

    @ApiModelProperty("担保费减免下限")
    private BigDecimal guaranteeLower;
    @ApiModelProperty("担保费减免上限")
    private BigDecimal guaranteeUpper;
    @ApiModelProperty("担保费减免最大值")
    private BigDecimal guaranteeMax;

    @ApiModelProperty("逾期费减免下限")
    private BigDecimal lateLower;
    @ApiModelProperty("逾期费减免上限")
    private BigDecimal lateUpper;
    @ApiModelProperty("逾期费减免最大值")
    private BigDecimal lateMax;

    @ApiModelProperty("提前结清费减免下限")
    private BigDecimal advSettLower;
    @ApiModelProperty("提前结清费减免上限")
    private BigDecimal advSettUpper;
    @ApiModelProperty("提前结清费减免最大值")
    private BigDecimal advSettMax;

    @ApiModelProperty("担保费减免下限")
    private BigDecimal fee1Lower;
    @ApiModelProperty("担保费减免上限")
    private BigDecimal fee1Upper;
    @ApiModelProperty("担保费减免最大值")
    private BigDecimal fee1Max;

    @ApiModelProperty("反担保费减免下限")
    private BigDecimal fee2Lower;
    @ApiModelProperty("反担保费减免上限")
    private BigDecimal fee2Upper;
    @ApiModelProperty("反担保费减免最大值")
    private BigDecimal fee2Max;

    @ApiModelProperty("贷后逾期管理费减免下限")
    private BigDecimal fee3Lower;
    @ApiModelProperty("贷后逾期管理费减免上限")
    private BigDecimal fee3Upper;
    @ApiModelProperty("贷后逾期管理费减免最大值")
    private BigDecimal fee3Max;

    @ApiModelProperty("催费减免下限")
    private BigDecimal fee6Lower;
    @ApiModelProperty("催费减免上限")
    private BigDecimal fee6Upper;
    @ApiModelProperty("催费减免最大值")
    private BigDecimal fee6Max;

    @ApiModelProperty("罚息减免下限")
    private BigDecimal ointLower;
    @ApiModelProperty("罚息减免上限")
    private BigDecimal ointUpper;
    @ApiModelProperty("罚息减免最大值")
    private BigDecimal ointMax;
}
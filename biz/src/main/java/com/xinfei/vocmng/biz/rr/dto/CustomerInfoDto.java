/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version $ OrderDto, v 0.1 2023-12-19 14:39 junjie.yan Exp $
 */
@Data
public class CustomerInfoDto {

    @ApiModelProperty(value = "userNo")
    private String userNo;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "身份证")
    private String idNo;

    @ApiModelProperty(value = "注册app")
    private String app;

    @ApiModelProperty(value = "来源app")
    private String sourceChannel;

    @ApiModelProperty(value = "账号状态")
    private Integer status;

    @ApiModelProperty(value = "注册时间")
    private Date registerTime;

    @ApiModelProperty(value = "借款成功订单数")
    private Integer sucLoanOrdersNum;

}
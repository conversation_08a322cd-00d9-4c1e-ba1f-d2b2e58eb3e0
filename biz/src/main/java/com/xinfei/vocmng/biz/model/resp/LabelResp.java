/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinfei.vocmng.biz.model.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ LabelResp, v 0.1 2023/12/20 20:40 wancheng.qu Exp $
 */

@Data
public class LabelResp implements Serializable {

    @ApiModelProperty(value = "标签id")
    private Long id;

    @ApiModelProperty(value = "标签类型")
    private String type;

    @ApiModelProperty(value = "标签名")
    private String name;

    @ApiModelProperty(value = "标签颜色")
    private String color;

    @ApiModelProperty(value = "标签方案内容")
    private String solution;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "标签可选状态：1可选 0不可选")
    private Integer displayState;


}
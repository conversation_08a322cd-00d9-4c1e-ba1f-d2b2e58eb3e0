/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ DeductRuleEnum, v 0.1 2024-05-21 14:38 junjie.yan Exp $
 */

@Getter
public enum OrderType {
    //订单类型，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）
    MAIN("MAIN", "现金订单"),
    PROFIT("PROFIT", "营收订单");

    private final String code;
    private final String description;

    OrderType(String code, String description) {
        this.code = code;
        this.description = description;
    }

}
/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ GetOrderListRequest, v 0.1 2023-12-18 15:16 junjie.yan Exp $
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class GetOrderListRequest extends PageQuery {

    @ApiModelProperty("用户号列表")
    private List<String> userNos;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "手机号查CustNo")
    private String mobileCust;

    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "custNo")
    private String custNo;

    @ApiModelProperty(value = "注册APP")
    private String app;

    @ApiModelProperty(value = "订单状态列表:RISK_AUDITING(01, 风控审核中)," +
            "RISK_AUDIT_PASS(11, 风控审核通过)," +
            "FUND_LENDING(20, 放款处理中)," +
            "SUCCESS(03, 交易成功)," +
            "FAIL(04, 交易失败);")
    private List<String> orderStatuses;

    @ApiModelProperty("订单类型列表，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）")
    private String orderType;

    @ApiModelProperty(value = "订单号列表")
    private List<String> orderNos;

    @ApiModelProperty(value = "借据号列表")
    private List<String> loanNos;

    @ApiModelProperty(value = "创建开始时间")
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "创建结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "借据状态. RP-正常, OD-逾期, FP-结清")
    private List<String> loanStatus;

    @ApiModelProperty(value = "资方订单号")
    private String outOrderNumber;

    @ApiModelProperty(value = "资方借据号")
    private String thirdLoanNo;

    @ApiModelProperty(value = "渠道方订单号")
    private String channelOrderNumber;

    @ApiModelProperty(value = "最后结清时间——(LCS)开始")
    private LocalDateTime dateSettleStart;

    @ApiModelProperty(value = "最后结清时间——(LCS)结束")
    private LocalDateTime dateSettleEnd;

    @ApiModelProperty(value = "是否查询用户详情页")
    private Boolean isCustomerDetail;

    @ApiModelProperty(value = "innerAppList")
    private List<String> innerAppList;

    @ApiModelProperty(value = "capitalPoolList")
    private List<String> capitalPoolList;
}
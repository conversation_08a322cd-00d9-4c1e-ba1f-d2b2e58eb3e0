/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 借据列表DTO
 *
 * <AUTHOR>
 * @version $ LoanListDto, v 0.1 2025-07-21 shaohui.chen Exp $
 */
@Data
public class LoanListDto {

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("实际费率")
    private BigDecimal actualFeeRate;

    @ApiModelProperty("已还总额")
    private BigDecimal totalPaidAmount;

    @ApiModelProperty("剩余应还")
    private BigDecimal remainingAmount;

    @ApiModelProperty("最大可减免金额")
    private BigDecimal maxDeductionAmount;

    @ApiModelProperty("最小费率")
    private BigDecimal minFeeRate;

}

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.template;

import com.xinfei.vocmng.biz.log.DigestLogHolder;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.enums.TechplayErrScenarioEnum;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.util.ExceptionUtil;
import com.xinfei.vocmng.biz.util.LoggerUtil;
import com.xinfei.xfframework.common.BaseResponse;
import org.springframework.dao.DataAccessException;

/**
 * 查询模版
 *
 * <AUTHOR>
 * @version $ QueryTemplate, v 0.1 2023/8/28 16:46 Jinyan.Huang Exp $
 */
public class QueryTemplate extends AbstractTemplate {

    /**
     * 场景码
     */
    private static final TechplayErrScenarioEnum ERR_SCENARIO = TechplayErrScenarioEnum.QUERY;

    /**
     * 查询模板
     *
     * @param response 查询结果
     * @param callBack 回调接口
     */
    public static void query(BaseResponse response, QueryCallBack callBack) {

        try {

            callBack.checkParameter();
            callBack.doQuery();

            buildSuccessResponse(response);

        } catch (TechplayException e) {

            LoggerUtil.warn(LOGGER, "查询服务异常:", e);
            buildFailureResponse(response, ERR_SCENARIO, e);

        } catch (DataAccessException ex) {

            ExceptionUtil.error(ex, "查询服务出现数据库层异常:");
            buildFailureResponse(response, ERR_SCENARIO, TechplayErrDtlEnum.DB_EXCEPTION,
                    "查询服务出现数据库层异常");

        } catch (RuntimeException ex) {

            ExceptionUtil.error(ex, "查询服务出现未知异常:");
            buildFailureResponse(response, ERR_SCENARIO, TechplayErrDtlEnum.UNKNOWN_EXCEPTION,
                    "查询服务出现未知异常");

        } finally {

            DigestLogHolder.set(callBack.buildDigestLog());
        }
    }
}

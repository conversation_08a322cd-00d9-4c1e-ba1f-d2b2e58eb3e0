/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ OrderDto, v 0.1 2023-12-19 14:39 junjie.yan Exp $
 */
@Data
public class CustomerDto {

    @ApiModelProperty(value = "userNo")
    private String userNo;

    @ApiModelProperty(value = "客户号")
    private String custNo;

    @ApiModelProperty(value = "姓名")
    @DataPermission(type = DataPermissionType.MASK_NAME)
    private String name;

    @ApiModelProperty(value = "手机号")
    @DataPermission(type = DataPermissionType.MASK_TELE)
    private String mobile;

    @ApiModelProperty(value = "手机号明文")
    private String tel;

    @ApiModelProperty(value = "身份证明文")
    private String idNoMd;

    @ApiModelProperty(value = "身份证")
    @DataPermission(type = DataPermissionType.MASK_IDCARD)
    private String idNo;

    @ApiModelProperty(value = "注册app")
    private String app;

    @ApiModelProperty(value = "来源app")
    private String sourceChannel;

    @ApiModelProperty(value = "用户状态 0：注销  10：正常 20：临时注销 2：注销中")
    private Integer status;

    @ApiModelProperty(value = "注册时间")
    private LocalDateTime registerTime;

    @ApiModelProperty(value = "借款成功订单数")
    private Integer sucLoanOrdersNum;

}
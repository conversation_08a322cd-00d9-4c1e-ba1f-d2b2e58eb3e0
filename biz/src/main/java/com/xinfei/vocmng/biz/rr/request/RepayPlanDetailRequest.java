/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RepayPlanRequest, v 0.1 2024-03-28 16:47 junjie.yan Exp $
 */
@Data
public class RepayPlanDetailRequest {

    @ApiModelProperty("借据号")
    @NotNull(message = "loanNo不能为空")
    private String loanNo;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("账单号")
    private String billNo;

    @ApiModelProperty("订单类型，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）")
    private String orderType;

    @ApiModelProperty("本金减免金额")
    private BigDecimal prinReduct;

    @ApiModelProperty("利息减免金额")
    private BigDecimal intReduct;

    @ApiModelProperty("担保费减免金额")
    private BigDecimal guarantReduct;

    @ApiModelProperty("逾期费减免金额")
    private BigDecimal lateReduct;

    @ApiModelProperty("挡板减免金额")
    private BigDecimal redReduct;

    @ApiModelProperty("抵扣金额")
    private BigDecimal deduction;

    @ApiModelProperty("原来应还")
    private BigDecimal oldRealAmt;

    @ApiModelProperty(value = "实际还款金额")
    @NotNull(message = "repaymentAmount不能为空")
    private BigDecimal repaymentAmount;

    @ApiModelProperty("期数")
    private List<String> terms;

    @ApiModelProperty("实际费率")
    private BigDecimal realRatio;
}
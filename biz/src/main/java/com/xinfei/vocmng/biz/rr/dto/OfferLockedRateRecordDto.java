package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * offer锁定费率记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24 04:58:06
 */
@Data
public class OfferLockedRateRecordDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("自增id")
    private Long id;

    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("应用费率")
    private Integer applyRate;

    @ApiModelProperty("减免金额（元）")
    private BigDecimal deductAmt;

    @ApiModelProperty("操作")
    private String operation;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("更新人")
    private String updater;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updatedTime;

    @ApiModelProperty("逻辑删除")
    private Integer isDel;
}

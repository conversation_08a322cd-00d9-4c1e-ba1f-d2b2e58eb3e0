/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.model.req.UserConsultationReq;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.service.SpmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 * @version $ SpmApi, v 0.1 2024/5/13 16:31 wancheng.qu Exp $
 */
@Api(tags = "打点相关相关")
@RestController
@RequestMapping("/spm")
public class SpmApi {

    @Autowired
    private SpmService spmService;
    @ApiOperation("用户访问客服大厅数据上报")
    @PostMapping("/userConsultation")
    public ApiResponse<Boolean> userConsultation(@RequestBody UserConsultationReq req){
        return ApiResponse.success(spmService.saveUserData(req));
    }

}
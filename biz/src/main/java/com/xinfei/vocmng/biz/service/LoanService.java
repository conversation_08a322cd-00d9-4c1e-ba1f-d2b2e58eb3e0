package com.xinfei.vocmng.biz.service;

import com.xinfei.huttalegal.facade.rr.res.LegalAgencyDetail;
import com.xinfei.vocmng.biz.rr.dto.DiversionOrderDto;
import com.xinfei.vocmng.biz.rr.dto.IVRLatestBillDto;
import com.xinfei.vocmng.biz.rr.dto.IVRLoanSettlementDto;
import com.xinfei.vocmng.biz.rr.dto.IVRMonthlyAmountDto;
import com.xinfei.vocmng.biz.rr.dto.IVROrderInfoDto;
import com.xinfei.vocmng.biz.rr.dto.OrderDto;
import com.xinfei.vocmng.biz.rr.dto.RepaymentsDto;
import com.xinfei.vocmng.biz.rr.dto.bill.LoanPlanDto;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.CanRepayResponse;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.itl.rr.ProductDetailInfo;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
public interface LoanService {

    Paging<OrderDto> queryOrderList(GetOrderListRequest request);

    OrderDto queryOrderDetail(GetOrderDetailRequest request);

    Boolean orderCancel(String orderNo);

    String queryByOutOrderNumber(GetOrderListRequest request);

    String queryByChannelOrderNumber(GetOrderListRequest request);

    List<LoanPlanDto> queryBillList(GetBillListRequest request);

    /**
     * 分页查询账单列表
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    Paging<LoanPlanDto> queryBillListWithPaging(GetBillListPageRequest request);

    CanRepayResponse queryCanRepay(CanRepayRequest request);

    Paging<RepaymentsDto> queryRepayments(GetRepaymentsRequest request);

    Paging<DiversionOrderDto> queryApiOrderList(ApiOrderRequest apiOrderRequest);

    List<ProductDetailInfo> queryProductList();

    LegalAgencyDetail queryAgencyDetail(AgencyDetailRequest agencyDetailRequest);

    void orderListDownload(HttpServletResponse httpServletResponse,GetBillListRequest request);

    IVROrderInfoDto queryIVROrderList(String customNo, String mobile);

    /**
     * 查询IVR近期账单信息
     *
     * @param customNo 客户号
     * @param mobile   手机号
     * @return 近期账单信息
     */
    IVRLatestBillDto queryIVRLatestBill(String customNo, String mobile);

    /**
     * 查询IVR当月应还金额信息
     *
     * @param customNo 客户号
     * @param mobile   手机号
     * @return 当月应还金额信息
     */
    IVRMonthlyAmountDto queryIVRMonthlyAmount(String customNo, String mobile);

    /**
     * 查询IVR在贷未结清订单信息
     *
     * @param customNo 客户号
     * @param mobile   手机号
     * @return 在贷未结清订单信息
     */
    IVRLoanSettlementDto queryIVRLoanSettlement(String customNo, String mobile);
}

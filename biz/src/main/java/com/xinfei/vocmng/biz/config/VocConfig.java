/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.config;

import apollo.com.google.gson.reflect.TypeToken;
import com.google.gson.Gson;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Type;
import java.util.List;

/**
 * 整合类 config
 *
 * <AUTHOR>
 * @version $ Constants, v 0.1 2023/8/29 09:54 Chengsheng.Li Exp $
 */
@Data
@Configuration
@EnableFeignClients(basePackageClasses = {VocConfig.class})
@ComponentScan(basePackageClasses = {VocConfig.class})
public class VocConfig {

    private final Gson gson = new Gson();

    private final Type typeListString = new TypeToken<List<String>>() {
    }.getType();

    /**
     * 调用合同系统的ua和签名key配置信息
     */
    @Value("${contract.ua}")
    private String contractUA;
    @Value("${contract.sign.key}")
    private String contractKey;


    /**
     * 调用会员权益系统的appId和appSecret配置信息
     */
    @Value("${member.interest.appId}")
    private String memberInterestAppId;
    @Value("${member.interest.appSecret}")
    private String memberInterestAppSecret;

    /**
     * 呼叫中心签名相关的配置信息
     */
    @Value("${call.center.appKey}")
    private String callCenterAppKey;
    @Value("${call.center.appSecret}")
    private String callCenterAppSecret;

    /**
     * 工单系统url
     */
    @Value("${work.order.url}")
    private String workOrderUrlFormat;

    /**
     * 小结是否校验订单号合法性
     */
    @Value("${isValidOrderNo}")
    private String isValidOrderNo;

    /**
     * 是否小结弹屏
     */
    @Value("${isUDesk:false}")
    private Boolean isUDesk;

    /**
     * 是否小结弹屏
     */
    @Value("${isSummaryPop}")
    private String isSummaryPop;

    /**
     * 是否实时质检
     */
    @Value("${isRealtimeQuality:T}")
    private String isRealtimeQuality;

    /**
     * 名单服务域名
     */
    @Value("${xf.listcore.url}")
    private String listCoreUrl;

    /**
     * 结清证明接口开关
     */
    @Value("${xf.jqzm.flag:true}")
    private boolean jqzmFlag;

    @Value("${contract.new.interface:true}")
    private boolean contractNewInterface;

    @Value("${xf.jqzm.config:jmx_cash}")
    private String jqzmConfig;

    /**
     * 五要素开关
     */
    @Value("${xf.wys.flag:true}")
    private boolean wysFlag;

    @Value("${xf.mzxd.config:mzxd_cash}")
    private String mzxdConfig;

    @Value("${api_diversion.ua:xyf-union-backend}")
    private String apiDiversionUA;
    @Value("${api_diversion.sign.key:111111}")
    private String apiDiversionSignKey;


    @Value("${sso.open.flag:true}")
    private boolean ssoOpenFlag;

    @Value("${contract.open.flag:true}")
    private boolean newContractFlag;

    @Value("${isNewFeeControl:false}")
    private Boolean isNewFeeControl;

    @Value("${isNewRefund:false}")
    private Boolean isNewRefund;

    @Value("${isDeductFeeControl:false}")
    private Boolean isDeductFeeControl;

    @Value("${userIdentifyWhiteList}")
    private String userIdentifyWhiteList;

    @Value("${complaintChannels}")
    private String complaintChannels;

    @Value("${xf.jqzm.encipher:jmx_cash}")
    private String jqzmEncipher;

    /**
     * 是否走特征平台
     */
    @Value("${isFeatureQuery}")
    private String isFeatureQuery;

    @Value("${AntiFraudDay:1}")
    private Integer AntiFraudDay;

    @Value("${AntiFraudApp}")
    private String AntiFraudApp;

    @Value("${xf.acs.appKey:customer_service_system}")
    private String acsAppKey;

    @Value("${xf.voc.systemCode:vocmng}")
    private String systemCode;

    @Value("${customer.detail.url}")
    private String customerDetailURL;

    @Value("${workOrder.detail.url}")
    private String workOrderDetailURL;

    /**
     * 特殊对公短信模板配置
     */
    @Value("${specialPublicAccountSMS}")
    private String specialPublicAccountSMS;

    /**
     * 架构对应默认进线来源
     */
    @Value("${departmentNameSourceList}")
    private String departmentNameSourceList;

    @Value("${fundSourceWhiteList}")
    private String fundSourceWhiteList;

    /**
     * udesk质检推送开关
     */
    @Value("${pushUDeskQualityCheck:false}")
    private Boolean pushUDeskQualityCheck;

    /**
     * 飞跃会员开关
     */
    @Value("${isSuperVipCard:false}")
    private Boolean isSuperVipCard;

    /** xyf下线开关 */
    @Value("${isStandardBatch:false}")
    private Boolean isStandardBatch;

    public Boolean newFeeControl() {
        if (isNewFeeControl) {
            List<String> whiteList = gson.fromJson(userIdentifyWhiteList, typeListString);
            if (!whiteList.contains(UserContextHolder.getUserContext().getUserIdentify())) {
                return true;
            }
        }
        return false;
    }
}

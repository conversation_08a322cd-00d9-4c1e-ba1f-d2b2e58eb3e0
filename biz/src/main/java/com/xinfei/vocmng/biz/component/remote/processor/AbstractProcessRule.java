package com.xinfei.vocmng.biz.component.remote.processor;

import cn.hutool.core.util.ArrayUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.xinfei.vocmng.biz.component.remote.RemoteProcessor;
import com.xinfei.vocmng.biz.component.remote.RemoteProcessStrategy;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.model.annotation.ProcessRule;
import com.xinfei.xfframework.common.JsonUtil;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Iterator;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2023/12/17
 */
@Slf4j
public abstract class AbstractProcessRule implements InitializingBean, RemoteProcessor {
    @Autowired
    private RemoteProcessStrategy headerBuilderStrategy;
    @Autowired
    protected VocConfig vocConfig;

    @Override
    public void afterPropertiesSet() throws Exception {
        if(!this.getClass().isAnnotationPresent(ProcessRule.class)){
            throw new IllegalArgumentException("HeaderBuilder need @ProcessRule, pls check the processor config.");
        }

        init();
    }



    /**
     * 将简单实体转换为map结构
     * @param t
     * @return
     * @param <T>
     */
    protected  <T> Map<String, String> toMap(T t){
        if(t == null){
            return Collections.EMPTY_MAP;
        }

        try {
            ObjectMapper objectMapper = JsonUtil.getObjMapper();
            JsonNode node = objectMapper.readTree(JsonUtil.toJsonNull(t));
            Iterator<Map.Entry<String, JsonNode>> iterator = node.fields();

            Map<String,String> headerMap = Maps.newHashMapWithExpectedSize(node.size());
            while (iterator.hasNext()){
                Map.Entry<String,JsonNode> curItem = iterator.next();
                Object curItemValue = curItem.getValue();
                if(curItemValue == null){
                    continue;
                }
                headerMap.put(curItem.getKey(),curItem.getValue().asText());

            }

            return headerMap;
        } catch (JsonProcessingException e) {
            log.error("parse header failed, header="+t,e);
        }

        return Collections.emptyMap();
    }

    /**
     * 注册请求头构造者
     */
    private void init(){
        ProcessRule construct = this.getClass().getAnnotation(ProcessRule.class);
        headerBuilderStrategy.register(construct.service().getAppName(),this);
    }

    /**
     * 获取请求body信息
     * @param template
     * @return
     */
    protected String getRequestBody(RequestTemplate template){
        byte[] bodyArray = template.body();
        if(ArrayUtil.isEmpty(bodyArray)){
            return null;
        }

        return new String(bodyArray);
    }
}

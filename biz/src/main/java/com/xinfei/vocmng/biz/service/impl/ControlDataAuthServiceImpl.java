/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.service.ControlDataAuthMappingService;
import com.xinfei.vocmng.dal.mapper.ControlDataAuthMappingMapper;
import com.xinfei.vocmng.dal.po.ControlDataAuthMapping;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ ControlDataAuthServiceImpl, v 0.1 2024/3/19 13:42 wancheng.qu Exp $
 */
@Service
public class ControlDataAuthServiceImpl extends BaseService<ControlDataAuthMappingMapper, ControlDataAuthMapping> implements ControlDataAuthMappingService{

    @Override
    @Transactional
    public void saveBatchs(List<ControlDataAuthMapping> list) {
        saveBatch(list);
    }
}
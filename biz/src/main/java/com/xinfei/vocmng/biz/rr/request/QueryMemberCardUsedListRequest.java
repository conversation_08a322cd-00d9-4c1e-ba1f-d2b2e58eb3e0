package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version $ MemberCardUsedRequest, v 0.1 2023/12/25 20:40 qu.lu Exp $
 */
@Data
public class QueryMemberCardUsedListRequest {
    @ApiModelProperty("会员卡id")
    @NotNull(message = "会员卡id不能为空")
    private Integer cardId;

    @ApiModelProperty("会员卡类型：1老会员卡  2新会员卡 3飞享会员 4飞跃会员")
    @NotNull(message = "会员卡类型不能为空")
    private Integer type;
}

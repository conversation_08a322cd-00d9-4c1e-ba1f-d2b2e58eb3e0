/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.job;

import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.dal.mapper.*;
import com.xinfei.vocmng.dal.po.*;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.DataCenterClientService;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.rr.MobileGetData;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.xyf.cis.query.facade.dto.standard.response.CustNoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ SummaryService, v 0.1 2024-07-10 17:05 junjie.yan Exp $
 */
@Component
@Slf4j
public class SummaryService {

    @Resource
    private UnionCustomerQuestionMapper unionCustomerQuestionMapper;

    @Resource
    private CommunicateSummaryCopyMapper communicateSummaryCopyMapper;

    @Resource
    private SummaryQuestionMapper summaryQuestionMapper;

    @Resource
    private IssueCategoryConfigMapper issueCategoryConfigMapper;

    @Resource
    private CommunicateSummaryRemarkCopyMapper communicateSummaryRemarkCopyMapper;

    @Resource
    private CisFacadeClientService clientService;

    @Resource
    private CisFacadeClient cisFacadeClient;

    @Resource
    private EmployeeMapper employeeMapper;

    @Resource
    private UnionQuestionRemarkMapper unionQuestionRemarkMapper;

    @Resource
    private DataCenterClientService dataCenterClientService;

    @Value(value = "${summaryService:true}")
    private Boolean isSwitch;

    @Value(value = "${summaryServiceSize:1}")
    private Integer summaryServiceSize;

    @XxlJob("summary")
    public ReturnT<String> summary(String params) {

        XxlJobLogger.log("summary job start, params={}", params);
        if (StringUtils.isEmpty(params)) {
            return new ReturnT<>("params is empty params=" + params);
        }

        String[] param = params.split(",");
        long min = Long.parseLong(param[0]);
        long max = Long.parseLong(param[1]);

        log.info(LogUtil.infoLog("summaryStart", params + "min:" + param[0] + "max:" + param[1]));
        LocalDateTime localDateTime = LocalDateTime.now();
        try {
            Long oldCount = unionCustomerQuestionMapper.queryCount(min, max);
            int length = 100000;
            Long newCount = communicateSummaryCopyMapper.queryCount(min + length, max + length);
            log.info(LogUtil.infoLog("summaryCount", "oldCount:" + oldCount + "---newCount:" + newCount));
            if (!oldCount.equals(newCount)) {
                throw new TechplayException(TechplayErrDtlEnum.SUMMARY_ERROR, "新老小结数不等");
            }

            long idStart = min;
            long idEnd = idStart + summaryServiceSize;
            while (idEnd <= max) {
                LocalDateTime localDateTime1 = LocalDateTime.now();
                log.info(LogUtil.infoLog("summaryProcessStart", "id range:" + idStart + "---" + idEnd));

                List<SummaryEntity> oldSummary = unionCustomerQuestionMapper.queryQuestionByRange(idStart, idEnd);
                List<CommunicateSummaryCopy> newSummary = communicateSummaryCopyMapper.querySummaryByRange(idStart + length, idEnd + length);
                List<CommunicateSummaryRemarkCopy> newRemark = communicateSummaryRemarkCopyMapper.queryRemarkByRange(idStart + length, idEnd + length);

                //结束
                if (CollectionUtils.isEmpty(oldSummary) && CollectionUtils.isEmpty(newSummary) && CollectionUtils.isEmpty(newRemark)) {
                    break;
                }

                if ((CollectionUtils.isEmpty(oldSummary) && CollectionUtils.isNotEmpty(newSummary)) || (CollectionUtils.isNotEmpty(oldSummary) && CollectionUtils.isEmpty(newSummary))) {
                    throw new TechplayException(TechplayErrDtlEnum.SUMMARY_ERROR, "新老小结数不等");
                }

                if (oldSummary.size() != newSummary.size()) {
                    throw new TechplayException(TechplayErrDtlEnum.SUMMARY_ERROR, "新老小结数不等");
                }

                if ((CollectionUtils.isEmpty(oldSummary) && CollectionUtils.isNotEmpty(newRemark)) || (CollectionUtils.isNotEmpty(oldSummary) && CollectionUtils.isEmpty(newRemark))) {
                    throw new TechplayException(TechplayErrDtlEnum.SUMMARY_ERROR, "新老小结备注数不等");
                }

                if (oldSummary.size() != newRemark.size()) {
                    throw new TechplayException(TechplayErrDtlEnum.SUMMARY_ERROR, "新老小结备注数不等");
                }

                insertVoc(oldSummary, newSummary, newRemark);

                LocalDateTime localDateTime2 = LocalDateTime.now();
                Duration dur = Duration.between(localDateTime1, localDateTime2);
                log.info(LogUtil.infoLog("summaryProcessEnd", "id range:" + idStart + "---" + idEnd + "   time:" + dur.toMillis()));
                idStart += summaryServiceSize;
                idEnd = idStart + summaryServiceSize;
            }

            LocalDateTime localDateTime3 = LocalDateTime.now();
            Duration dur = Duration.between(localDateTime, localDateTime3);
            log.info(LogUtil.infoLog("summaryEnd", "id range:" + (idStart - summaryServiceSize) + "---" + (idEnd - summaryServiceSize) + "   time:" + dur.toMillis()));

        } catch (Exception e) {
            // 打印XxlJob的log日志
            XxlJobLogger.log("summary job is error", e);
            // 打印服务器日志
            LogUtil.clientErrorLog("Job", "summary", null, null, e);
            throw new TechplayException(TechplayErrDtlEnum.SUMMARY_ERROR, e.getMessage());
        }

        return new ReturnT<>("success");
    }


    public void insertVoc(List<SummaryEntity> oldSummarys, List<CommunicateSummaryCopy> newSummarys, List<CommunicateSummaryRemarkCopy> newRemarks) {

        for (SummaryEntity oldSummary : oldSummarys) {
            CommunicateSummaryCopy newSummary = newSummarys.stream().filter(r -> r.getId().equals(oldSummary.getId() + 100000)).findFirst().orElse(null);
            if (newSummary == null) {
                throw new TechplayException(TechplayErrDtlEnum.SUMMARY_ERROR, "新小结表中无老小结表对应id数据");
            }

            CommunicateSummaryRemarkCopy newRemark = newRemarks.stream().filter(r -> r.getCommunicateSummaryId().equals(oldSummary.getId() + 100000)).findFirst().orElse(null);
            if (newRemark == null) {
                throw new TechplayException(TechplayErrDtlEnum.SUMMARY_ERROR, "新小结备注表中无老小结表对应id数据");
            }

            try {
                SpecialEntity specialEntity = extractedSpecial(oldSummary);
                check(oldSummary, newSummary, newRemark, specialEntity);
            } catch (Exception e) {
                log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + "===" + e));
            }
        }
    }

    private SpecialEntity extractedSpecial(SummaryEntity oldSummary) {
        SpecialEntity specialEntity = new SpecialEntity();

        //todo 数仓问题分类 issue_category_lv1 issue_category_lv2
        if (isSwitch) {
            SummaryQuestion summaryQuestion = summaryQuestionMapper.queryBySummaryIdSummaryQuestion(oldSummary.getId());
            if (summaryQuestion != null) {
                if (StringUtils.isNotEmpty(summaryQuestion.getQuestionLv1())) {
                    Long id = issueCategoryConfigMapper.queryIssueId(summaryQuestion.getQuestionLv1().toLowerCase(), 1, 0L);
                    specialEntity.setIssueCategoryLv1(id == null ? 349L : id);
                } else {
                    specialEntity.setIssueCategoryLv1(349L);
                }

                if (StringUtils.isNotEmpty(summaryQuestion.getQuestionLv2())) {
                    Long id = issueCategoryConfigMapper.queryIssueId(summaryQuestion.getQuestionLv2().toLowerCase(), 2, specialEntity.getIssueCategoryLv1());
                    specialEntity.setIssueCategoryLv2(id == null ? 350L : id);
                } else {
                    specialEntity.setIssueCategoryLv2(350L);
                }
            } else {
                specialEntity.setIssueCategoryLv1(349L);
                specialEntity.setIssueCategoryLv2(350L);
            }
        }

        // 用户来源、投诉用户来源 source complaint_source
        if (oldSummary.getQuestionType() == 0) {
            if ("客服热线".equals(oldSummary.getUserSource())) {
                specialEntity.setSource(1);
            } else if ("在线客服".equals(oldSummary.getUserSource())) {
                specialEntity.setSource(2);
            } else {
                specialEntity.setSource(3);
            }
        } else {
            specialEntity.setSource(5);
            specialEntity.setComplaintSource(oldSummary.getUserSource());
        }

        // 身份证密文转custNo
        if (StringUtils.isNotEmpty(oldSummary.getIdCardNumberCipher())) {
            String idCard = clientService.getDecryptByField("idCard", Collections.singletonList(oldSummary.getIdCardNumberCipher()));
            if (StringUtils.isNotEmpty(idCard) && !idCard.contains("*")) {
                CustNoDTO custNoDTO = cisFacadeClient.queryCustNoByIdNo(idCard);
                if (custNoDTO != null) {
                    specialEntity.setCustNo(custNoDTO.getCustNo());
                }
            }
        }

        // 创建人姓名 create_user_identify
        if (StringUtils.isNotEmpty(oldSummary.getCreatePersonCipher())) {
            String name = clientService.getDecryptByField("name", Collections.singletonList(oldSummary.getCreatePersonCipher()));
            if (StringUtils.isNotEmpty(name)) {
                List<String> createUserIdentifys = employeeMapper.getUserIdentifyByName(name);
                if (CollectionUtils.isEmpty(createUserIdentifys)) {
                    specialEntity.setCreateUserIdentify("root");
                } else {
                    specialEntity.setCreateUserIdentify(createUserIdentifys.get(0));
                }
            } else {
                specialEntity.setCreateUserIdentify("root");
            }
        }

        // 跟进记录 follow_up_record
        if (oldSummary.getQuestionType() != null && oldSummary.getQuestionType() == 1) {
            List<UnionQuestionRemark> unionQuestionRemark = unionQuestionRemarkMapper.getRemarkByQuestionId(oldSummary.getId());
            if (CollectionUtils.isNotEmpty(unionQuestionRemark)) {
                specialEntity.setFollowUpRecord(unionQuestionRemark.get(0).getRemark());
            }
        }

        //进线手机号
        if (StringUtils.isNotEmpty(oldSummary.getInMobileProtyle())) {
            MobileGetData mobileGetData = dataCenterClientService.mobileGet(oldSummary.getInMobileProtyle());
            if (mobileGetData != null && StringUtils.isNotEmpty(mobileGetData.getMobile())) {
                specialEntity.setTelephone(clientService.getEncodeMobileLocal(mobileGetData.getMobile()));
            }
        }

        //回电手机号
        if (StringUtils.isNotEmpty(oldSummary.getOutMobileProtyle())) {
            MobileGetData mobileGetData = dataCenterClientService.mobileGet(oldSummary.getOutMobileProtyle());
            if (mobileGetData != null && StringUtils.isNotEmpty(mobileGetData.getMobile())) {
                specialEntity.setCallBackMobile(clientService.getEncodeMobileLocal(mobileGetData.getMobile()));
            }
        }

        return specialEntity;
    }

    public void check(SummaryEntity oldSummary, CommunicateSummaryCopy newSummary, CommunicateSummaryRemarkCopy newRemark, SpecialEntity specialEntity) {
        //============newSummary
        //user_id
        if (oldSummary.getUserId() != null && !oldSummary.getUserId().equals(newSummary.getUserId())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:user_id"));
        }
        //order_no
        if (StringUtils.isNotEmpty(oldSummary.getOrderNumber()) && !oldSummary.getOrderNumber().equals(newSummary.getOrderNo())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:order_no"));
        }
        //is_deleted
        if (newSummary.getIsDeleted() != null && !newSummary.getIsDeleted().equals(0)) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:is_deleted"));
        }
        //created_time
        if (oldSummary.getCreateTime() != null && !oldSummary.getCreateTime().equals(newSummary.getCreatedTime())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:created_time"));
        }
        //updated_time
        if (oldSummary.getUpdateTime() != null && !oldSummary.getUpdateTime().equals(newSummary.getUpdatedTime())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:updated_time"));
        }
        //sus_black_market
        if (oldSummary.getIsDarkIndustry() != null && !oldSummary.getIsDarkIndustry().equals(newSummary.getSusBlackMarket())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:sus_black_market"));
        }
        //black_market_reason
        if (StringUtils.isNotEmpty(oldSummary.getDarkIndustryReason()) && !oldSummary.getDarkIndustryReason().equals(newSummary.getBlackMarketReason())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:black_market_reason"));
        } else if (StringUtils.isEmpty(oldSummary.getDarkIndustryReason()) && StringUtils.isNotEmpty(newSummary.getBlackMarketReason())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:black_market_reason"));
        }
        //status
        if (newSummary.getStatus() != null && !newSummary.getStatus().equals(3)) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:status"));
        }
        //telephone_encrypted
        if (StringUtils.isNotEmpty(specialEntity.getTelephone()) && !specialEntity.getTelephone().equals(newSummary.getTelephoneEncrypted())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:in_mobile_protyle"));
        } else if (StringUtils.isEmpty(specialEntity.getTelephone()) && StringUtils.isNotEmpty(newSummary.getTelephoneEncrypted())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:in_mobile_protyle"));
        }
        //call_back_mobile_encrypted
        if (StringUtils.isNotEmpty(specialEntity.getCallBackMobile()) && !specialEntity.getCallBackMobile().equals(newSummary.getCallBackMobileEncrypted())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:out_mobile_protyle"));
        } else if (StringUtils.isEmpty(specialEntity.getCallBackMobile()) && StringUtils.isNotEmpty(newSummary.getCallBackMobileEncrypted())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:out_mobile_protyle"));
        }
        //question_type
        if (oldSummary.getQuestionType() != null && !oldSummary.getQuestionType().equals(newSummary.getQuestionType())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:question_type"));
        }

        //create_user_identify
        if (StringUtils.isNotEmpty(specialEntity.getCreateUserIdentify()) && !specialEntity.getCreateUserIdentify().equals(newSummary.getCreateUserIdentify())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:create_user_identify"));
        }

        if (isSwitch) {
            if (specialEntity.getIssueCategoryLv1() != null && !specialEntity.getIssueCategoryLv1().equals(newSummary.getIssueCategoryLv1())) {
                log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:issue_category_lv1"));
            }

            if (specialEntity.getIssueCategoryLv2() != null && !specialEntity.getIssueCategoryLv2().equals(newSummary.getIssueCategoryLv2())) {
                log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:issue_category_lv2"));
            }
        }

        if (specialEntity.getSource() != null && !specialEntity.getSource().equals(newSummary.getSource())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:source"));
        }

        if (StringUtils.isNotEmpty(specialEntity.getComplaintSource()) && !specialEntity.getComplaintSource().equals(newSummary.getComplaintSource())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:complaint_source"));
        } else if (StringUtils.isEmpty(specialEntity.getComplaintSource()) && StringUtils.isNotEmpty(newSummary.getComplaintSource())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:complaint_source"));
        }

        if (StringUtils.isNotEmpty(specialEntity.getCustNo()) && !specialEntity.getCustNo().equals(newSummary.getCustNo())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:cust_no"));
        } else if (StringUtils.isEmpty(specialEntity.getCustNo()) && StringUtils.isNotEmpty(newSummary.getCustNo())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:cust_no"));
        }

        //===========remark

        //question_content
        if (StringUtils.isNotEmpty(oldSummary.getQuestionContent()) && !oldSummary.getQuestionContent().equals(newRemark.getRemark())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:remark.remark"));
        } else if (StringUtils.isEmpty(oldSummary.getQuestionContent()) && StringUtils.isNotEmpty(newRemark.getRemark())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:remark.remark"));
        }

        //is_deleted
        if (newRemark.getIsDeleted() != null && !newRemark.getIsDeleted().equals(0)) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:remark.is_deleted"));
        }
        //created_time
        if (oldSummary.getCreateTime() != null && !oldSummary.getCreateTime().equals(newRemark.getCreatedTime())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:remark.created_time"));
        }
        //updated_time
        if (oldSummary.getUpdateTime() != null && !oldSummary.getUpdateTime().equals(newRemark.getUpdatedTime())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:remark.updated_time"));
        }
        //follow_up_record
        if (StringUtils.isNotEmpty(specialEntity.getFollowUpRecord()) && !specialEntity.getFollowUpRecord().equals(newRemark.getFollowUpRecord())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:remark.follow_up_record"));
        } else if (StringUtils.isEmpty(specialEntity.getFollowUpRecord()) && StringUtils.isNotEmpty(newRemark.getFollowUpRecord())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:remark.follow_up_record"));
        }

        //create_user_identify
        if (StringUtils.isNotEmpty(specialEntity.getCreateUserIdentify()) && !specialEntity.getCreateUserIdentify().equals(newRemark.getCreateUserIdentify())) {
            log.info(LogUtil.infoLog("columnError", "oldSummary id: " + oldSummary.getId() + " column:remark.create_user_identify"));
        }


    }


}
/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version $ HuttaDetailRequest, v 0.1 2024-06-21 16:13 junjie.yan Exp $
 */
@Data
public class HuttaDetailRequest {


    @ApiModelProperty("方案明细编号")
    @NotNull(message = "方案明细编号必传")
    private String planDetailId;

    @ApiModelProperty("借据号")
    @NotNull(message = "借据号必传")
    private String loanNo;

    @ApiModelProperty("用户号")
    @NotNull(message = "用户号号必传")
    private String userNo;


}
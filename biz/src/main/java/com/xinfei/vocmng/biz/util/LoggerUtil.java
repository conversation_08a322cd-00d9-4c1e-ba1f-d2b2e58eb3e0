/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import org.slf4j.Logger;

/**
 * 规范化日志打印工具，注意日志的级别选择：<br>
 *
 * <p>
 *   <ol>
 *     <li>DEBUG <b>开发环境</b>应用调试，输出详细的应用状态
 *     <li>INFO  <b>生产环境</b>运行状态观察，输出应用生命周期的中<b>正常重要事件</b>
 *     <li>WARN  <b>生产环境</b>故障诊断，输出应用中的<b>可预期的异常事件</b>
 *     <li>ERROR <b>生产环境</b>境故障诊断，输出应用中的<b>未预期的异常事件</b>
 *   </ol>
 * </p>
 *
 * <AUTHOR>
 * @version $ LoggerUtil, v 0.1 2023/8/28 20:35 Jinyan.Huang Exp $
 */
@SuppressWarnings("unused")
public final class LoggerUtil {

    /**
     * 禁用构造函数
     */
    private LoggerUtil() {
        // 禁用构造函数
    }

    /**
     * 生成<font color="blue">调试</font>级别日志<br>
     * 可处理任意多个输入参数，并避免在日志级别不够时字符串拼接带来的资源浪费
     *
     * @param logger 日志对象
     * @param obj    日志参数
     */
    public static void debug(Logger logger, Object... obj) {
        if (logger.isDebugEnabled()) {
            logger.debug(getLogString(obj));
        }
    }

    /**
     * 生成<font color="blue">通知</font>级别日志<br>
     * 可处理任意多个输入参数，并避免在日志级别不够时字符串拼接带来的资源浪费
     *
     * @param logger 日志对象
     * @param obj    日志参数
     */
    public static void info(Logger logger, Object... obj) {
        if (logger.isInfoEnabled()) {
            logger.info(getLogString(obj));
        }
    }

    /**
     * 生成<font color="brown">警告</font>级别日志<br>
     * 可处理任意多个输入参数，并避免在日志级别不够时字符串拼接带来的资源浪费
     *
     * @param logger 日志对象
     * @param obj    日志参数
     */
    public static void warn(Logger logger, Object... obj) {
        logger.warn(getLogString(obj));
    }

    /**
     * 生成<font color="brown">错误</font>级别日志<br>
     * 可处理任意多个输入参数，并避免在日志级别不够时字符串拼接带来的资源浪费
     *
     * @param logger 日志对象
     * @param obj    日志参数
     */
    public static void error(Logger logger, Object... obj) {
        logger.error(getLogString(obj));
    }

    /**
     * 生成<font color="brown">错误</font>级别日志<br>
     * 可处理任意多个输入参数，并避免在日志级别不够时字符串拼接带来的资源浪费,打印堆栈
     *
     * @param logger 日志对象
     * @param e      异常堆栈
     * @param obj    日志参数
     */
    public static void error(Logger logger, Throwable e, Object... obj) {
        logger.error(getLogString(obj), e);
    }

    /**
     * 生成输出到日志的字符串
     *
     * @param obj 任意个要输出到日志的参数
     * @return 日志输出字符串
     */
    public static String getLogString(Object... obj) {
        StringBuilder log = new StringBuilder();
        for (Object o : obj) {
            log.append(o);
        }
        return log.toString();
    }

}

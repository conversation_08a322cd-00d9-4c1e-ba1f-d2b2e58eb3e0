/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.log;

import cn.hutool.json.JSONObject;
import com.xinfei.vocmng.biz.rr.TeamQueryRequest;
import com.xinfei.xfframework.common.BaseRequest;
import lombok.Setter;

/**
 * 查询摘要日志
 *
 * <AUTHOR>
 * @version $ QueryDigestLog, v 0.1 2023/8/28 20:32 Jinyan.Huang Exp $
 */
@Setter
final public class QueryDigestLog extends BaseDigestLog {

    /**
     * 团队编码
     */
    private String teamCode;

    /**
     * 团队编码
     */
    private String appName;

    /**
     * 根据团队信息查询请求构造器日志对象
     *
     * @param teamQueryRequest 团队信息查询请求
     */
    public QueryDigestLog(TeamQueryRequest teamQueryRequest) {

        if (teamQueryRequest == null) {
            return;
        }

        this.teamCode = teamQueryRequest.getTeamCode();
    }

    public QueryDigestLog(BaseRequest teamQueryRequest) {

        if (teamQueryRequest == null) {
            return;
        }

//        this.teamCode = teamQueryRequest.getTeamCode();
    }

    /**
     * 根据团队信息查询请求构造器日志对象
     *
     * @param teamQueryRequest 团队信息查询请求
     */
    public QueryDigestLog(com.xinfei.vocmng.itl.rr.TeamQueryRequest teamQueryRequest) {

        if (teamQueryRequest == null) {
            return;
        }

        this.teamCode = teamQueryRequest.getTeamCode();
    }

    public QueryDigestLog(String appName) {

        this.appName = appName;
    }

    /**
     * 日志业务信息组装
     *
     * @param buffer 日志信息
     */
    protected void composeBizInfo(StringBuilder buffer) {
        buffer.append("(");
        JSONObject json = new JSONObject();
        {
            json.putOnce("teamCode", teamCode);
            json.putOnce("appName", appName);
        }
        buffer.append(json.toJSONString(0));
        buffer.append(")");
    }

}

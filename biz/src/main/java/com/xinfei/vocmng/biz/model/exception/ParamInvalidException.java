package com.xinfei.vocmng.biz.model.exception;

import com.xinfei.vocmng.biz.model.enums.ResultCode;
import lombok.Getter;

@Getter
public class ParamInvalidException extends RuntimeException {
    public Long code;
    public String message;

    public ParamInvalidException(Long code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public ParamInvalidException(String message) {
        super(message);
        this.code = ResultCode.FAILED.getCode();
        this.message = message;
    }

    public ParamInvalidException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }

    public ParamInvalidException(ResultCode resultCode, String message) {
        super(resultCode.getMessage() + message);
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage() + message;
    }
}

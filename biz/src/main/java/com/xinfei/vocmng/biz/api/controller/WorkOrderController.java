package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.vocmng.biz.api.WorkOrderApi;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.remote.WorkOrderRemoteService;
import com.xinfei.vocmng.biz.rr.dto.WorkOrderDetailDto;
import com.xinfei.vocmng.biz.rr.dto.WorkOrderSimpleDto;
import com.xinfei.vocmng.biz.rr.request.QueryWorkOrderRequest;
import com.xinfei.vocmng.biz.rr.request.QueryWorkOrderUrlRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version $ WorkOrderController, v 0.1 2024/1/15 19:59 qu.lu Exp $
 */
@Slf4j
@Validated
@RestController
@LoginRequired
public class WorkOrderController implements WorkOrderApi {

    @Autowired
    private WorkOrderRemoteService workOrderRemoteService;

    @Override
    public ApiResponse<PageResultResponse<WorkOrderDetailDto>> queryWorkOrderDetail(QueryWorkOrderRequest request) {
        return ApiResponse.success(workOrderRemoteService.queryWorkOrderDetail(request));
    }

    @Override
    public ApiResponse<WorkOrderSimpleDto> loadWorkOrderUrl(QueryWorkOrderUrlRequest request) {
        return ApiResponse.success(workOrderRemoteService.loadWorkOrderUrl(request));
    }
}

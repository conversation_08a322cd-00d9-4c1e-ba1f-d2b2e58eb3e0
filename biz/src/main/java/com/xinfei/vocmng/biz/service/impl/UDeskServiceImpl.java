/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import apollo.com.google.gson.Gson;
import apollo.com.google.gson.JsonObject;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinfei.vocmng.biz.config.CustomEventPublisher;
import com.xinfei.vocmng.biz.config.DepartmentNameSource;
import com.xinfei.vocmng.biz.config.DepartmentNameSourceConfig;
import com.xinfei.vocmng.biz.config.DisplayNumber;
import com.xinfei.vocmng.biz.config.DisplayNumberConfig;
import com.xinfei.vocmng.biz.config.QualityEventPublisher;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.model.req.SetComSummaryRequest;
import com.xinfei.vocmng.biz.service.UDeskService;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.vocmng.dal.mapper.CommunicateSummaryMapper;
import com.xinfei.vocmng.dal.mapper.DepartmentMapper;
import com.xinfei.vocmng.dal.mapper.EmployeeMapper;
import com.xinfei.vocmng.dal.mapper.ImCallLogsDetailsMapper;
import com.xinfei.vocmng.dal.mapper.ImCallLogsMapper;
import com.xinfei.vocmng.dal.mapper.ImSessionDetailsMapper;
import com.xinfei.vocmng.dal.mapper.ImSessionsMapper;
import com.xinfei.vocmng.dal.mapper.RobotSessionsDetailsMapper;
import com.xinfei.vocmng.dal.mapper.RobotSessionsMapper;
import com.xinfei.vocmng.dal.po.CommunicateSummary;
import com.xinfei.vocmng.dal.po.Employee;
import com.xinfei.vocmng.dal.po.ImCallLogs;
import com.xinfei.vocmng.dal.po.ImCallLogsDetails;
import com.xinfei.vocmng.dal.po.ImSessionDetails;
import com.xinfei.vocmng.dal.po.ImSessions;
import com.xinfei.vocmng.dal.po.RobotSessions;
import com.xinfei.vocmng.dal.po.RobotSessionsDetails;
import com.xinfei.vocmng.itl.client.feign.impl.UdeskClientService;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.rr.ImCallLogDetailsResponse;
import com.xinfei.vocmng.itl.rr.ImCallLogQueryResponse;
import com.xinfei.vocmng.itl.rr.ImCallLogResponse;
import com.xinfei.vocmng.itl.rr.ImSessionsDetailsQueryResponse;
import com.xinfei.vocmng.itl.rr.ImSessionsDetailsResponse;
import com.xinfei.vocmng.itl.rr.ImSessionsListQueryResponse;
import com.xinfei.vocmng.itl.rr.ImSessionsResponse;
import com.xinfei.vocmng.itl.rr.RobotSessionsDetailsQueryResponse;
import com.xinfei.vocmng.itl.rr.RobotSessionsDetailsResponse;
import com.xinfei.vocmng.itl.rr.RobotSessionsQueryResponse;
import com.xinfei.vocmng.itl.rr.RobotSessionsRequest;
import com.xinfei.vocmng.itl.rr.RobotSessionsResponse;
import com.xinfei.vocmng.itl.rr.SessionsVoteResponse;
import com.xinfei.vocmng.itl.rr.acsdatacore.ACSPushData;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xinfei.vocmng.util.distributedLock.DistributedLock;
import com.xinfei.vocmng.util.threadpool.ContextInheritableThreadPoolExecutor;
import com.xinfei.vocmng.util.threadpool.ParallelStreamUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version $ UDeskServiceImpl, v 0.1 2024-02-20 17:31 junjie.yan Exp $
 */

@Service
@Slf4j
public class UDeskServiceImpl implements UDeskService {

    @Autowired
    private VocConfig vocConfig;

    @Autowired
    private CustomEventPublisher eventPublisher;

    @Autowired
    private QualityEventPublisher qualityEventPublisher;

    @Resource
    private UdeskClientService udeskClientService;

    @Resource
    private EmployeeMapper employeeMapper;

    @Resource
    private DisplayNumberConfig displayNumberConfig;

    @Autowired
    private CommunicateSummaryMapper communicateSummaryMapper;

    @Resource
    private CisFacadeClientService cisFacadeClientService;

    @Resource
    private QualityInspectionImp qualityInspectionImp;

    @Resource
    private ImSessionsMapper imSessionsMapper;

    @Resource
    private ImSessionDetailsMapper imSessionDetailsMapper;

    @Resource
    private ImCallLogsMapper imCallLogsMapper;

    @Resource
    private ImCallLogsDetailsMapper imCallLogsDetailsMapper;

    @Resource
    private RobotSessionsMapper robotSessionsMapper;

    @Resource
    private RobotSessionsDetailsMapper robotSessionsDetailsMapper;

    @Resource
    private CisFacadeClientService cisFacadeClient;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private DepartmentMapper departmentMapper;


    @Value(value = "${km.udesk.robotIds}")
    private String robotIds;

    @Resource
    private ACSCallDataPushService acsCallDataPushService;

    @Resource
    private ACSIMDataPushService acsimDataPushService;

    @Resource
    private DepartmentNameSourceConfig departmentNameSourceConfig;


    @Override
    public String setComSummary(SetComSummaryRequest request, String name) {
        log.info(LogUtil.infoLog("uDesk接入", JsonUtil.toJson(request)));

        try {
            //获取员工信息
            Employee employee = getEmployee(request);

            //弹屏相关
            if (request.getOccasion().equals("agent_incall")) {
                String displayNumber = isPopScreen(request);
                if (StringUtils.isNotEmpty(displayNumber)) {
                    createSummary(request, employee.getUserIdentify(), displayNumber, employee.getDepartmentId());
                }
            }

            //质检相关
            if (employee.getRealTimeAssistance() == 1 && "T".equals(vocConfig.getIsRealtimeQuality())) {
                qualityEventPublisher.publishEvent(employee.getUserIdentify(), request.getCustomer_phone(), request.getDisplay_number(), request.getCall_id(), name);
                qualityInspectionImp.create(request.getCall_id(), employee.getId().toString(), name, request.getCustomer_phone());
            }
        } catch (Exception e) {
            log.error(LogUtil.infoLog("uDeskError_uDesk接通后续流程出错", e));
            throw new TechplayException(TechplayErrDtlEnum.UDESK_ERROR, "uDeskError_uDesk接通后续流程出错");
        }

        return "";
    }

    @Override
    public void sessionsSync(String startTime, String endTime) {
        int total = 1;
        int totalVote = 1;
        List<ImSessions> imSessionsList = new ArrayList<>();
        List<ImSessionDetails> imSessionDetailsList = new ArrayList<>();
        Map<Long,String> optionNameMap = new HashMap<>();

        LocalDateTime originalEndTime = LocalDateTimeUtils.parseLocalDateTimeByDateStr(endTime);
        LocalDateTime modifiedEndTime = originalEndTime.plus(30, ChronoUnit.MINUTES);
        String voteTime = LocalDateTimeUtils.format(modifiedEndTime);
        try {
            while (true) {
                SessionsVoteResponse sessionsVoteResponse = udeskClientService.sessionsVote(500, totalVote, startTime, voteTime);

                if (CollectionUtils.isNotEmpty(sessionsVoteResponse.getItem())) {
                    sessionsVoteResponse.getItem().forEach(vote -> {
                        if (vote.getSessionId() != null && vote.getSurveyOptionId() != null) {
                            optionNameMap.put(vote.getSessionId(), convertSurveyOptionIdToChinese(vote.getSurveyOptionId()));
                        }
                    });
                }
                if (total == sessionsVoteResponse.getTotalPages()) {
                    break;
                }
                totalVote++;
            }
            while (true) {
                ImSessionsListQueryResponse imSessionsQueryResponse = udeskClientService.sessionsSync(500, total, startTime, endTime);
                List<ImSessionsResponse> imSessionsResponsesList = imSessionsQueryResponse.getItem();

                // 取出所有待加密的对象集合
                List<String> encryptList = imSessionsResponsesList.stream()
                        .flatMap(imSessionsResponse -> Stream.of(
                                imSessionsResponse.getAgentNickName(),
                                imSessionsResponse.getCustomerNameCipher()
                        ))
                        .distinct()
                        .collect(Collectors.toList());

                //过滤对话未关闭的数据
                imSessionsResponsesList = imSessionsResponsesList.stream()
                        .filter(imSession -> imSession.getClosedAt() != null)  // 过滤掉 closedAt 为空的数据
                        .collect(Collectors.toList());

                // 批量加密
                Map<String, String> encryptKey = cisFacadeClient.getEncodeMobileMapKey(encryptList);

                // 处理会话数据
                for (ImSessionsResponse imSessionsResponse : imSessionsResponsesList) {
                    ImSessions imSessions = new ImSessions();
                    BeanUtil.copyProperties(imSessionsResponse, imSessions);
                    imSessions.setAgentNickNameCipher(encryptKey.get(imSessionsResponse.getAgentNickName()));
                    imSessions.setCustomerNameCipher(encryptKey.get(imSessionsResponse.getCustomerNameCipher()));
                    imSessions.setOptionName(optionNameMap.get(imSessionsResponse.getSessionId()));
                    imSessions.setCreateTime(LocalDateTime.now());
                    imSessions.setUpdateTime(LocalDateTime.now());
                    imSessionsList.add(imSessions);
                }

                // 批量插入会话数据
                if (CollectionUtils.isNotEmpty(imSessionsList)) {
                    batchInsertData(imSessionsList, 500, imSessionsMapper::batchInsert);
                }

                // 批量查询会话详情数据
                List<Long> subSessionIdList = imSessionsList.stream()
                        .map(ImSessions::getSubSessionId)
                        .collect(Collectors.toList());

                // 使用CompletableFuture和自定义线程池替代parallelStream
                List<ImSessionsDetailsQueryResponse> detailsResponses = ParallelStreamUtil.parallelStream(
                        subSessionIdList,
                        10, // 自定义并行度，可以根据实际情况调整
                        imSessionsId -> udeskClientService.sessionDetailsSync(String.valueOf(imSessionsId), startTime, endTime)
                );

                // 处理会话详情数据
                for (ImSessionsDetailsQueryResponse detailsResponse : detailsResponses) {
                    List<ImSessionsDetailsResponse> imSubSessionLog = detailsResponse.getImLogInfos();
                    for (ImSessionsDetailsResponse imSessionsDetailsResponse : imSubSessionLog) {
                        ImSessionDetails imSessionDetails = new ImSessionDetails();
                        BeanUtil.copyProperties(imSessionsDetailsResponse, imSessionDetails);
                        imSessionDetails.setCreateTime(LocalDateTime.now());
                        imSessionDetails.setUpdateTime(LocalDateTime.now());
                        imSessionDetailsList.add(imSessionDetails);
                    }
                }

                // 批量插入会话详情数据
                if (CollectionUtils.isNotEmpty(imSessionDetailsList)) {
                    batchInsertData(imSessionDetailsList, 500, imSessionDetailsMapper::batchInsert);
                }

                // 发送数据
                if (CollectionUtils.isNotEmpty(imSessionsResponsesList)) {
                    Map<Long, List<ImSessionsDetailsResponse>> detailsMap = detailsResponses.stream()
                            .flatMap(response -> response.getImLogInfos().stream())
                            .collect(Collectors.groupingBy(ImSessionsDetailsResponse::getSubSessionId));

                    for (ImSessionsResponse sessions : imSessionsResponsesList) {
                        List<ImSessionsDetailsResponse> detailsForSession = detailsMap.getOrDefault(sessions.getSubSessionId(), Collections.emptyList());
                        // 创建 ACSPushData 对象并填充数据
                        sessions.setOptionName(optionNameMap.get(sessions.getSessionId()));
                        ACSPushData pushData = new ACSPushData();
                        pushData.setImSessionsResponse(sessions);
                        pushData.setImSessionsDetailsResponseList(detailsForSession);
                        acsimDataPushService.pushData(pushData);
                    }
                }

                // 清空列表
                imSessionsList.clear();
                imSessionDetailsList.clear();

                if (total == imSessionsQueryResponse.getTotalPages()) {
                    break;
                }
                total++;
            }
        } catch (Exception e) {
            log.error("Error occurred during sessionsSync", e);
        }
    }

    @Override
    public void callSync(String startTime, String endTime) {
        int total = 1;
        List<ImCallLogs> imCallLogsList = new ArrayList<>();
        List<ImCallLogsDetails> imCallLogsDetailsList = new ArrayList<>();
        try {
            while (true) {
                ImCallLogQueryResponse imCallLogQueryResponse = udeskClientService.callSync(50, total, startTime, endTime);
                List<ImCallLogResponse> imCallLogResponses = imCallLogQueryResponse.getItems();
                // 取出所有待加密的对象集合
                List<String> encryptList = imCallLogResponses.stream()
                        .flatMap(imCallLog -> Stream.of(
                                imCallLog.getAgentNickName(),
                                imCallLog.getCallNumberCipher(),
                                imCallLog.getUserNameCipher()
                        ))
                        .distinct()
                        .collect(Collectors.toList());
                // 批量加密
                Map<String, String> encryptKey = cisFacadeClient.getEncodeMobileMapKey(encryptList);
                // 处理通话日志数据
                for (ImCallLogResponse imCallLogResponse : imCallLogResponses) {
                    ImCallLogs imCallLogs = new ImCallLogs();
                    BeanUtil.copyProperties(imCallLogResponse, imCallLogs);
                    imCallLogs.setAgentNickNameCipher(encryptKey.get(imCallLogResponse.getAgentNickName()));
                    imCallLogs.setCallNumberCipher(encryptKey.get(imCallLogResponse.getCallNumberCipher()));
                    imCallLogs.setUserNameCipher(encryptKey.get(imCallLogResponse.getUserNameCipher()));
                    imCallLogs.setCreateTime(LocalDateTime.now());
                    imCallLogs.setUpdateTime(LocalDateTime.now());
                    imCallLogsList.add(imCallLogs);
                }
                // 批量插入通话日志数据
                if (CollectionUtils.isNotEmpty(imCallLogsList)) {
                    batchInsertData(imCallLogsList, 200, imCallLogsMapper::batchInsert);
                }
                // 发送数据
                if (CollectionUtils.isNotEmpty(imCallLogResponses)) {
                    List<ImCallLogResponse> imCallLogResponseList = imCallLogResponses.stream()
                            .peek(imCallLogResponse -> {
                                imCallLogResponse.setCallNumberOrigin(imCallLogResponse.getCallNumberCipher());
                                imCallLogResponse.setCallNumberCipher(encryptKey.get(imCallLogResponse.getCallNumberCipher()));
                                imCallLogResponse.setUserNameCipher(encryptKey.get(imCallLogResponse.getUserNameCipher()));
                            })
                            .collect(Collectors.toList());
                    // 创建 ACSPushData 对象并填充数据
                    ACSPushData pushData = new ACSPushData();
                    pushData.setImCallLogResponseList(imCallLogResponseList);
                    acsCallDataPushService.pushData(pushData);
                }
                // 批量查询通话详情数据
                List<String> callIdList = imCallLogsList.stream()
                        .map(ImCallLogs::getCallId)
                        .collect(Collectors.toList());

                // 使用CompletableFuture和自定义线程池替代parallelStream
                List<ImCallLogQueryResponse> detailsResponses = ParallelStreamUtil.parallelStream(
                        callIdList,
                        10, // 自定义并行度，可以根据实际情况调整
                        callId -> udeskClientService.callDetailsSync(String.valueOf(callId))
                );

                // 处理会话详情数据
                for (ImCallLogQueryResponse imCallLogResponse : detailsResponses) {
                    List<ImCallLogDetailsResponse> imCallLogDetailsResponses = imCallLogResponse.getCallLogs();
                    for (ImCallLogDetailsResponse imCallLogDetailsResponse : imCallLogDetailsResponses) {
                        ImCallLogsDetails imCallLogsDetails = new ImCallLogsDetails();
                        imCallLogsDetails.setAgentNickNameCipher(encryptKey.get(imCallLogDetailsResponse.getAgentNickName()));
                        imCallLogsDetails.setCallNumberCipher(encryptKey.get(imCallLogDetailsResponse.getCallNumberCipher()));
                        imCallLogsDetails.setUserNameCipher(encryptKey.get(imCallLogDetailsResponse.getUserNameCipher()));
                        BeanUtil.copyProperties(imCallLogDetailsResponse, imCallLogsDetails);
                        imCallLogsDetails.setCreateTime(LocalDateTime.now());
                        imCallLogsDetails.setUpdateTime(LocalDateTime.now());
                        imCallLogsDetailsList.add(imCallLogsDetails);
                    }
                }

                // 批量插入通话详情数据
                if (CollectionUtils.isNotEmpty(imCallLogsDetailsList)) {
                    batchInsertData(imCallLogsDetailsList, 500, imCallLogsDetailsMapper::batchInsert);
                }
                // 清空列表
                imCallLogsList.clear();
                imCallLogsDetailsList.clear();
                if (total == imCallLogQueryResponse.getTotalPages()) {
                    break;
                }
                total++;
            }
        } catch (Exception e) {
            log.error("Error occurred during callSync", e);
        }
    }

    @Override
    public void robotSync(String startTime, String endTime) {
        List<RobotSessions> robotSessionsList = new ArrayList<>();
        RobotSessionsRequest robotSessionsRequest = new RobotSessionsRequest();
        String robots[] = robotIds.split(",");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 因机器人数据不能超过100页数据 所以将时间段控制在2小时 减少单次查询的数据量
        LocalDateTime startDate = LocalDateTime.parse(startTime, formatter);
        LocalDateTime endDate = LocalDateTime.parse(endTime, formatter);
        LocalDateTime tempStartTime = startDate;
        while (tempStartTime.isBefore(endDate)) {
            LocalDateTime tempEndTime = tempStartTime.plusHours(2);
            if (tempEndTime.isAfter(endDate)) {
                tempEndTime = endDate;
            }
            for (int i = 0; i < robots.length; i++) {
                int pageNum = 1;
                int totalPages = 0;
                while (true) {
                    robotSessionsRequest.setPageNum(pageNum);
                    robotSessionsRequest.setPageSize(100);
                    robotSessionsRequest.setStartTime(LocalDateTimeUtils.format(tempStartTime));
                    robotSessionsRequest.setEndTime(LocalDateTimeUtils.format(tempEndTime));
                    robotSessionsRequest.setRobotId(Integer.parseInt(robots[i]));
                    RobotSessionsQueryResponse robotSessionsQueryResponse = udeskClientService.robotSync(robotSessionsRequest);
                    totalPages = (int) Math.ceil((double) robotSessionsQueryResponse.getPaging().getTotal() / robotSessionsQueryResponse.getPaging().getPageSize());
                    List<RobotSessionsResponse> robotSessionsResponses = robotSessionsQueryResponse.getData();
                    for (RobotSessionsResponse robotSessionsResponse : robotSessionsResponses) {
                        RobotSessions robotSessions = new RobotSessions();
                        BeanUtil.copyProperties(robotSessionsResponse, robotSessions);
                        robotSessions.setCreateTime(LocalDateTime.now());
                        robotSessions.setUpdateTime(LocalDateTime.now());
                        robotSessionsList.add(robotSessions);
                    }
                    if (totalPages == 0 || pageNum == totalPages) {
                        break;
                    }
                    pageNum++;
                }
            }
            tempStartTime = tempEndTime;
        }
        batchInsertData(robotSessionsList, 500, robotSessionsMapper::batchInsert);
        robotSessionsList.clear();
    }

    @Override
    @DistributedLock(lockName = "'lock:' + #redisKey + ':' + T(java.time.LocalDateTime).now().format(T(java.time.format.DateTimeFormatter).ofPattern('yyyyMMddHHmm'))",
            lockTime = 5, retryTimes = 3)
    public void robotDetailsSync(String redisKey ,String startTime, String endTime) {
        log.info(LogUtil.infoLog("uDeskTask", "uDesk机器人数据同步开始 startTime:{} endTime:{}", startTime, endTime));
        System.out.println("uDesk机器人数据同步开始");
        List<RobotSessions> robotSessionsList = new ArrayList<>();
        List<RobotSessionsDetails> robotSessionsDetailsList = new ArrayList<>();
        List<Long> robotSessionsIdList = new ArrayList<>();
        RobotSessionsRequest robotSessionsRequest = new RobotSessionsRequest();
        String robots[] = robotIds.split(",");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 因机器人数据不能超过100页数据 所以将时间段控制在4小时 减少单次查询的数据量
        LocalDateTime startDate = LocalDateTime.parse(startTime, formatter);
        LocalDateTime endDate = LocalDateTime.parse(endTime, formatter);
        LocalDateTime tempStartTime = startDate;
        while (tempStartTime.isBefore(endDate)) {
            LocalDateTime tempEndTime = tempStartTime.plusHours(4);
            if (tempEndTime.isAfter(endDate)) {
                tempEndTime = endDate;
            }
            for (int i = 0; i < robots.length; i++) {
                int pageNum = 1;
                int totalPages = 0;
                while (true) {
                    robotSessionsRequest.setPageNum(pageNum);
                    robotSessionsRequest.setPageSize(100);
                    robotSessionsRequest.setStartTime(LocalDateTimeUtils.format(tempStartTime));
                    robotSessionsRequest.setEndTime(LocalDateTimeUtils.format(tempEndTime));
                    robotSessionsRequest.setRobotId(Integer.parseInt(robots[i]));
                    RobotSessionsQueryResponse robotSessionsQueryResponse = udeskClientService.robotSync(robotSessionsRequest);
                    totalPages = (int) Math.ceil((double) robotSessionsQueryResponse.getPaging().getTotal() / robotSessionsQueryResponse.getPaging().getPageSize());
                    List<RobotSessionsResponse> robotSessionsResponses = robotSessionsQueryResponse.getData();
                    for (RobotSessionsResponse robotSessionsResponse : robotSessionsResponses) {
                        RobotSessions robotSessions = new RobotSessions();
                        BeanUtil.copyProperties(robotSessionsResponse, robotSessions);
                        robotSessions.setCreateTime(LocalDateTime.now());
                        robotSessions.setUpdateTime(LocalDateTime.now());
                        robotSessionsList.add(robotSessions);
                        robotSessionsIdList.add(robotSessions.getSessionId());
                    }
                    if (totalPages == 0 || pageNum == totalPages) {
                        break;
                    }
                    pageNum++;
                }
            }
            tempStartTime = tempEndTime;
        }


        if (CollectionUtils.isNotEmpty(robotSessionsList)) {
            robotSessionsList.clear();
            for (Long robotSessionsId : robotSessionsIdList) {
                int detailsPageNum = 1;
                int detailsTotalPages = 0;
                while (true) {
                    RobotSessionsDetailsQueryResponse robotSessionsDetailsQueryResponse =
                            udeskClientService.robotDetailsSync(robotSessionsId, detailsPageNum, 100, startTime, endTime);
                    List<RobotSessionsDetailsResponse> robotSessionsDetailsResponses = robotSessionsDetailsQueryResponse.getData();
                    detailsTotalPages = (int) Math.ceil((double) robotSessionsDetailsQueryResponse.getPaging().getTotal() / robotSessionsDetailsQueryResponse.getPaging().getPageSize());
                    for (RobotSessionsDetailsResponse robotSessionsDetailsResponse : robotSessionsDetailsResponses) {
                        RobotSessionsDetails robotSessionsDetails = new RobotSessionsDetails();
                        BeanUtil.copyProperties(robotSessionsDetailsResponse, robotSessionsDetails);
                        robotSessionsDetails.setUpdateTime(LocalDateTime.now());
                        robotSessionsDetailsList.add(robotSessionsDetails);
                    }
                    if (detailsTotalPages == 0 || detailsPageNum == detailsTotalPages) {
                        break;
                    }
                    detailsPageNum++;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(robotSessionsDetailsList)) {
            batchInsertData(robotSessionsDetailsList, 500, robotSessionsDetailsMapper::batchInsert);
        }
    }

    // 创建用于执行同步任务的线程池
    private ThreadPoolExecutor createSyncTaskThreadPool() {
        return new ContextInheritableThreadPoolExecutor(
                3,                                         // 核心线程数
                5,                                         // 最大线程数
                60L,                                       // 空闲线程存活时间
                TimeUnit.SECONDS,                          // 时间单位
                new LinkedBlockingQueue<>(10),             // 工作队列
                new CustomizableThreadFactory("udesk-sync-"), // 线程工厂
                new ThreadPoolExecutor.CallerRunsPolicy()  // 拒绝策略
        );
    }

    @DistributedLock(lockName = "'lock:' + #redisKey + ':' + T(java.time.LocalDateTime).now().format(T(java.time.format.DateTimeFormatter).ofPattern('yyyyMMddHHmm'))",
            lockTime = 5, retryTimes = 3)
    public void uDeskSyncAll(String redisKey, String startTime, String endTime) {
        log.info(LogUtil.infoLog(redisKey, "uDesk数据同步开始 startTime:{} endTime:{}", startTime, endTime));
        // 创建线程池
        ThreadPoolExecutor executor = createSyncTaskThreadPool();
        try {
            // 创建任务列表
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            // 添加会话同步任务
            CompletableFuture<Void> sessionsFuture = CompletableFuture.runAsync(
                    () -> sessionsSync(startTime, endTime), executor);
            futures.add(sessionsFuture);
            // 添加通话同步任务
            CompletableFuture<Void> callFuture = CompletableFuture.runAsync(
                    () -> callSync(startTime, endTime), executor);
            futures.add(callFuture);
            // 如果是uDeskSync，添加机器人同步任务
            if ("uDeskSync".equals(redisKey)) {
                CompletableFuture<Void> robotFuture = CompletableFuture.runAsync(
                        () -> robotSync(startTime, endTime), executor);
                futures.add(robotFuture);
            }
            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0]));
            // 等待所有任务完成或超时
            try {
                allFutures.get(110, TimeUnit.SECONDS); // 设置超时时间略小于Redis锁的过期时间
                log.info("All uDesk sync tasks completed successfully");
            } catch (Exception e) {
                log.error("Error waiting for uDesk sync tasks to complete", e);
            }
        } catch (Exception e) {
            log.error("Error during uDesk data synchronization", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }

            // 释放Redis锁
            redisUtils.del(redisKey);
        }
    }


    public <T> void batchInsertData(List<T> dataList, int batchSize, Consumer<List<T>> insertFunction) {
        //数据库批量插入方法
        if (CollectionUtils.isNotEmpty(dataList)) {
            int totalSize = dataList.size();
            int batchCount = (totalSize + batchSize - 1) / batchSize;
            for (int i = 0; i < batchCount; i++) {
                int start = i * batchSize;
                int end = Math.min(start + batchSize, totalSize);
                List<T> subList = dataList.subList(start, end);
                insertFunction.accept(subList);
            }
        }
    }

    private Employee getEmployee(SetComSummaryRequest request) {
        //根据坐席id获取坐席手机号
        String mobile = udeskClientService.getAgent(request.getAgent_id()).getCellphone();
        log.info(LogUtil.infoLog("uDesk接通获取坐席手机号,agentId:,mobile:", request.getAgent_id(), mobile));

        //根据坐席手机号获取identify
        LambdaQueryWrapper<Employee> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(mobile), Employee::getMobileEncrypted, cisFacadeClientService.getEncodeMobileLocal(mobile))
                .eq(Employee::getIsDeleted, 0)
                .eq(Employee::getState, 0);
        Employee employee = employeeMapper.selectOne(wrapper);
        String userIdentify = employee != null ? employee.getUserIdentify() : null;

        if (StringUtils.isEmpty(userIdentify)) {
            throw new TechplayException(TechplayErrDtlEnum.UDESK_ERROR, "uDeskError_此坐席手机号无法获取userIdentify,手机号{}" + mobile);
        }
        log.info(LogUtil.infoLog("uDesk接通获取坐席userIdentify,agentId:{},mobile:{},userIdentify:{}", request.getAgent_id(), mobile, userIdentify));
        return employee;
    }

    private void createSummary(SetComSummaryRequest request, String userIdentify, String displayNumber, Long departmentId) {
        //创建初始会话小结
        CommunicateSummary cr = new CommunicateSummary();
        String departmentName = departmentMapper.getNameById(departmentId);

        //默认来源-其他
        cr.setSource(3);
        //获取部门名称对应默认来源
        List<JsonObject>  departmentNameSourceList = departmentNameSourceConfig.getDepartmentNameSourceList();
        for (JsonObject jsonObject : departmentNameSourceList) {
            DepartmentNameSource departmentNameSource = new Gson().fromJson(jsonObject, DepartmentNameSource.class);
            if(departmentName.equals(departmentNameSource.getDepartmentName())){
                cr.setSource(departmentNameSource.getSource());
            }
        }

        cr.setCallId(request.getCall_id());
        cr.setTelephone(request.getCustomer_phone()); //todo @Deprecated
        cr.setTelephoneEncrypted(cisFacadeClientService.getEncodeMobileLocal(request.getCustomer_phone()));
        cr.setCreateUserIdentify(userIdentify);
        cr.setStatus(1);
        cr.setIssueCategoryLv1(-1L);
        cr.setIssueCategoryLv2(-1L);
        cr.setRelayNumber(displayNumber);
        cr.setCreatedTime(LocalDateTime.now());
        cr.setUpdatedTime(LocalDateTime.now());
        communicateSummaryMapper.insert(cr);
        Long summaryId = cr.getId();

        if (summaryId <= 0) {
            throw new TechplayException(TechplayErrDtlEnum.UDESK_ERROR, "uDeskError_接通后创建会话小结失败");
        }

        //通知前端，传入来电手机号、会话小结id、identify、中继号
        log.info(LogUtil.infoLog("uDesk接通后开始通知前端,userIdentify:{},customer_phone:{},display_number:{},summaryId:{}", userIdentify, request.getCustomer_phone(), request.getDisplay_number(), summaryId));
        eventPublisher.publishEvent(userIdentify, request.getCustomer_phone(), request.getDisplay_number(), summaryId);
    }

    private String isPopScreen(SetComSummaryRequest request) {
        //弹屏开关
        if (!"T".equals(vocConfig.getIsSummaryPop())) {
            return "";
        }

        //中继号过滤
        List<JsonObject> udeskDisplayNumbers = displayNumberConfig.getUdeskDisplayNumber();
        List<DisplayNumber> udeskDisplayNumberList = new ArrayList<>();
        for (JsonObject jsonObject : udeskDisplayNumbers) {
            DisplayNumber displayNumber = new Gson().fromJson(jsonObject, DisplayNumber.class);
            udeskDisplayNumberList.add(displayNumber);
        }
        udeskDisplayNumberList = udeskDisplayNumberList.stream().filter(r -> request.getDisplay_number().contains(r.getNumber())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(udeskDisplayNumberList)) {
            log.info(LogUtil.infoLog("此中继号未配置弹屏功能", request.getDisplay_number()));
            return "";
        }
        DisplayNumber displayNumber = udeskDisplayNumberList.get(0);
        if (!"1".equals(displayNumber.getStatus())) {
            log.info(LogUtil.infoLog("此中继号不在灰度范围内", request.getDisplay_number()));
            return "";
        }

        return displayNumber.getNumber();
    }

    public String convertSurveyOptionIdToChinese(Integer surveyOptionId) {
        if (surveyOptionId == null) {
            return "未知评价"; // Handle null surveyOptionId
        }
        switch (surveyOptionId) {
            case 218505:
                return "非常不满意";
            case 218504:
                return "不满意";
            case 218503:
                return "一般";
            case 218502:
                return "满意";
            case 218501:
                return "非常满意";
            default:
                return "其他"; // Handle any other unexpected values
        }
    }
}
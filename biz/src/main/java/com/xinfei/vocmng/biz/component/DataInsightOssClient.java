package com.xinfei.vocmng.biz.component;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.xinfei.vocmng.biz.config.DataInsightOssConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据洞察OSS客户端
 *
 * <AUTHOR>
 * @version $ DataInsightOssClient, v 0.1 2025/7/16 10:00 shaohui.chen Exp $
 */
@Component
@Slf4j
public class DataInsightOssClient {

    @Autowired
    private DataInsightOssConfig dataInsightOssConfig;

    /**
     * 创建OSS客户端
     */
    private OSS createOssClient() {
        return new OSSClientBuilder().build(
                dataInsightOssConfig.getEndpoint(),
                dataInsightOssConfig.getAccessKeyId(),
                dataInsightOssConfig.getAccessKeySecret()
        );
    }

    /**
     * 读取OSS文件内容
     *
     * @param ossPath OSS文件路径
     * @param fileName 文件名
     * @return 文件内容行列表
     */
    public List<String> readFileLines(String ossPath, String fileName) {
        List<String> lines = new ArrayList<>();
        OSS ossClient = null;
        OSSObject ossObject = null;
        BufferedReader reader = null;

        try {
            ossClient = createOssClient();
            String fullPath = ossPath + "/" + fileName;

            log.info("开始读取OSS文件: bucket={}, path={}",
                    dataInsightOssConfig.getBucketName(), fullPath);

            ossObject = ossClient.getObject(dataInsightOssConfig.getBucketName(), fullPath);
            reader = new BufferedReader(new InputStreamReader(ossObject.getObjectContent(), StandardCharsets.UTF_8));

            String line;
            while ((line = reader.readLine()) != null) {
                lines.add(line);
            }

            log.info("成功读取OSS文件，共{}行数据", lines.size());

        } catch (Exception e) {
            log.error("读取OSS文件失败: bucket={}, path={}/{}",
                    dataInsightOssConfig.getBucketName(), ossPath, fileName, e);
            throw new RuntimeException("读取OSS文件失败", e);
        } finally {
            closeResource(reader);
            closeResource(ossObject);
            if (ossClient != null) {
                try {
                    ossClient.shutdown();
                } catch (Exception e) {
                    log.error("关闭OSS客户端失败", e);
                }
            }
        }
        return lines;
    }

    /**
     * 关闭资源
     */
    private void closeResource(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                log.error("关闭资源失败", e);
            }
        }
    }
}

package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.vocmng.biz.api.OfferManageApi;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.FeeRateViewDto;
import com.xinfei.vocmng.biz.rr.dto.LoanListDto;
import com.xinfei.vocmng.biz.rr.dto.OfferLockedRateRecordDto;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.FeeRateInvalidResp;
import com.xinfei.vocmng.biz.rr.response.OfferInvalidResponse;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.biz.service.OfferManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Offer管理平台Controller
 *
 * <AUTHOR>
 * @version $ OfferManageController, v 0.1 2025-07-21 shaohui.chen Exp $
 */
@RestController
@Slf4j
@LoginRequired
public class OfferManageController implements OfferManageApi {

    @Resource
    private OfferManageService offerManageService;

    @Override
    public ApiResponse<List<LoanListDto>> queryLoanList(LoanListRequest request) {
        return ApiResponse.success(offerManageService.queryLoanList(request));
    }

    @Override
    public ApiResponse<Boolean> checkFuturePrincipalLessThanDeduction(FuturePrincipalCheckRequest request) {
        return ApiResponse.success(offerManageService.checkFuturePrincipalLessThanDeduction(request));
    }

    @Override
    public ApiResponse<String> confirmFeeRate(ConfirmFeeRateRequest request) {
        return ApiResponse.success(offerManageService.confirmFeeRate(request));
    }

    @Override
    public ApiResponse<List<FeeRateViewDto>> queryFeeRateList(FeeRateViewRequest request) {
        return ApiResponse.success(offerManageService.queryFeeRateList(request));
    }

    @Override
    public ApiResponse<FeeRateInvalidResp> feeRateInvalid(FeeRateInvalidReq request) {
        List<OfferInvalidResponse> offerInvalidResponses = new ArrayList<>();
        for (String planDetailId : request.getPlanDetailIds()) {
            OfferInvalidResponse offerInvalidResponse = offerManageService.feeRateInvalid(planDetailId);
            offerInvalidResponses.add(offerInvalidResponse);
        }

        FeeRateInvalidResp feeRateInvalidResp = new FeeRateInvalidResp();
        feeRateInvalidResp.setOfferInvalidResponses(offerInvalidResponses);
        return ApiResponse.success(feeRateInvalidResp);
    }

    @Override
    public ApiResponse<Paging<OfferLockedRateRecordDto>> getFeeRateRecords(FeeRateRecordReq request) {
        return ApiResponse.success(offerManageService.getFeeRateRecords(request));
    }
}

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 未来期本金检查请求
 *
 * <AUTHOR>
 * @version $ FuturePrincipalCheckRequest, v 0.1 2025-07-21 shaohui.chen Exp $
 */
@Data
public class FuturePrincipalCheckRequest {

    @ApiModelProperty(value = "借据号", required = true)
    @NotBlank(message = "借据号不能为空")
    private String loanNo;

    @ApiModelProperty(value = "目标费率", required = true, notes = "百分比形式，如24表示24%")
    @NotNull(message = "目标费率不能为空")
    private BigDecimal targetFeeRatio;
}

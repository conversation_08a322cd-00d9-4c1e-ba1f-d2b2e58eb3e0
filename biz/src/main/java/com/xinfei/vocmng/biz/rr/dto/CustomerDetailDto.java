/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import com.xinfei.vocmng.dal.po.LabelDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ GetOrderListResponse, v 0.1 2023-12-18 15:16 junjie.yan Exp $
 */
@Data
public class CustomerDetailDto {

    //cis
    //https://www.tapd.cn/********/markdown_wikis/show/#11********001018289
    @ApiModelProperty(value = "userNo")
    private String userNo;

    @ApiModelProperty(value = "custNo")
    private String custNo;

    @ApiModelProperty(value = "姓名")
    @DataPermission(type = DataPermissionType.MASK_NAME)
    private String name;

    @ApiModelProperty(value = "手机号")
    @DataPermission(type = DataPermissionType.MASK_TELE)
    private String mobile;

    @ApiModelProperty(value = "手机号明文")
    private String tel;

    @ApiModelProperty(value = "手机号密文")
    private String enCodeTel;

    @ApiModelProperty(value = "身份证")
    @DataPermission(type = DataPermissionType.MASK_IDCARD)
    private String idNo;

    @ApiModelProperty(value = "身份证明文")
    private String idNoTrans;

    @ApiModelProperty(value = "注册app")
    private String app;

    @ApiModelProperty(value = "来源app")
    private String sourceChannel;

    @ApiModelProperty(value = "账号状态 0：注销  10：正常 20：临时注销 2：注销中")
    private Integer status;

    @ApiModelProperty(value = "注册时间")
    private LocalDateTime registerTime;

    //https://www.tapd.cn/********/markdown_wikis/show/#11********001018290
    @ApiModelProperty(value = "身份证地址")
    @DataPermission(type = DataPermissionType.MASK_ADDRESS)
    private String idAddress;

    //https://www.tapd.cn/********/markdown_wikis/show/#11********001017392@toc16
    @ApiModelProperty(value = "最近登录时间")
    private LocalDateTime lastLoginTime;

    //https://www.tapd.cn/********/markdown_wikis/show/#11********001017610
    @ApiModelProperty(value = "银行卡列表")
    private List<BankDto> banks;

    @ApiModelProperty(value = "家庭")
    private FamilyEduDto familyEdu;

    @ApiModelProperty(value = "工作")
    private JobDto job;

    @ApiModelProperty(value = "联系人")
    private List<ContactDto> contacts;

    @ApiModelProperty(value = "照片信息")
    private ImagesDto images;

    //ams
    @ApiModelProperty(value = "现金已放款金额")
    private BigDecimal cashDebitAmt;

    @ApiModelProperty(value = "现金授信额度")
    private BigDecimal cashCreditAmt;

    @ApiModelProperty(value = "消费已放款金额")
    private BigDecimal consumerDebitAmt;

    @ApiModelProperty(value = "消费授信额度")
    private BigDecimal consumerCreditAmt;

    //lcs
    @ApiModelProperty(value = "借款成功订单数")
    private Integer sucLoanOrdersNum;

    //voc
    @ApiModelProperty(value = "标签")
    private List<LabelDto> labels;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "生日")
    private String birthday;

    //udesk
    @ApiModelProperty(value = "近7天进线次数")
    private Integer callLogs;

    @ApiModelProperty(value = "近7天IM会话次数")
    private Integer imLogs;

    @ApiModelProperty(value = "近7天会话小结数")
    private Integer summaryLogs;

    //工单系统
    @ApiModelProperty(value = "工单量")
    private Integer taskQuantity;

    //飞享会员
    @ApiModelProperty(value = "是否是会员")
    private Boolean isVip;

    @ApiModelProperty(value = "是否续费")
    private Boolean renewStatus;

    //飞跃会员
    @ApiModelProperty(value = "是否是飞跃会员")
    private Boolean isSuperVip;

    @ApiModelProperty(value = "飞跃会员是否续费")
    private Boolean superVipRenewStatus;

    @ApiModelProperty(value = "账号注销状态 0-注销失败 1-已注销 2-待注销 3-无资格 4-取消注销")
    private Integer logOffStatus;

    @ApiModelProperty(value = "账号注销原因")
    private String logOffRemark;

    @ApiModelProperty(value = "账号注销时间")
    private String logOffTime;

    @ApiModelProperty(value = "账号注销操作人员")
    private String logOffOperatorName;

    @ApiModelProperty(value = "多卡轮扣状态")
    private String cardDeductStatus;

    //1：正常:未命中“风险用户”标签可以截屏
    //2：无法截屏:命中“风险用户”标签
    //3：无法截屏白名单:命中“风险用户”标签 但客服手动加白，可以截屏
    @ApiModelProperty(value = "是否截屏限制白名单")
    private String isScreenshotWhite = "1";
}
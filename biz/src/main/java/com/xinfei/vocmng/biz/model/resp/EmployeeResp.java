/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinfei.vocmng.biz.model.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ EmployeeResp, v 0.1 2023/12/20 18:42 wancheng.qu Exp $
 */
@Data
public class EmployeeResp implements Serializable {

    @ApiModelProperty(value = "员工编号")
    private Long id;

    @ApiModelProperty(value = "员工姓名")
    private String name;

    @ApiModelProperty(value = "userIdentify")
    private String userIdentify;

    @ApiModelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "组织")
    private String department;

    @ApiModelProperty(value = "组织id")
    private Long departmentId;

    @ApiModelProperty(value = "角色")
    private String role;

    @ApiModelProperty(value = "角色id")
    private String roleId;

    @ApiModelProperty(value = "员工账号状态：0正常 1关闭")
    private Integer state;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "(2:EHR 3：客服, 4：电销， 5: 催收)")
    private Integer sourceType;

    @ApiModelProperty(value = "是否实时质检辅助(0:否,1:是)")
    private Integer realTimeAssistance;


}
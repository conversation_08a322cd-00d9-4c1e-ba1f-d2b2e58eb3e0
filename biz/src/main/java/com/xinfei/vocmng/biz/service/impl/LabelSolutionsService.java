/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import apollo.com.google.gson.Gson;
import apollo.com.google.gson.JsonObject;
import com.xinfei.vocmng.biz.config.LabelSolution;
import com.xinfei.vocmng.biz.config.LabelSolutionConfig;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ LabelSolutionsService, v 0.1 2024-01-11 13:13 junjie.yan Exp $
 */
@Service
public class LabelSolutionsService {

    @Resource
    private LabelSolutionConfig labelSolutionConfig;

    public String getSolution(String labelType, String labelName, Double score) {
        double percent = score / 10;

        List<JsonObject> labelSolutions = labelSolutionConfig.getLabelSolutions();

        List<LabelSolution> labelSolutionsList = new ArrayList<>();
        for (JsonObject jsonObject : labelSolutions) {
            LabelSolution user = new Gson().fromJson(jsonObject, LabelSolution.class);
            labelSolutionsList.add(user);
        }

        labelSolutionsList = labelSolutionsList.stream()
                .filter(r -> labelType.equals(r.getType()))
                .filter(r -> labelName.equals(r.getName()))
                .filter(r -> percent > r.getPercent())
                .sorted(Comparator.comparing(LabelSolution::getPercent).reversed())
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(labelSolutionsList)) {
            return labelSolutionsList.get(0).getSolution();
        }

        return "";
    }

}
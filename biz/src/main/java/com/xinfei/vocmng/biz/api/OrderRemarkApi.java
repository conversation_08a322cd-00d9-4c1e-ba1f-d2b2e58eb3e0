package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.OrderRemarkDto;
import com.xinfei.vocmng.biz.rr.request.AddOrderRemarkRequest;
import com.xinfei.vocmng.biz.rr.request.QueryOrderRemarkRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ OrderRemarkApi, v 0.1 2024/1/13 14:34 qu.lu Exp $
 */
@Api(tags = "订单备注相关接口")
@RequestMapping("/order/remark")
public interface OrderRemarkApi {

    /**
     * 保存订单备注信息
     *
     * @param request
     * @return
     */
    @ApiOperation("保存订单备注信息")
    @PostMapping("/add")
    ApiResponse<Void> addOrderRemark(@RequestBody @Valid AddOrderRemarkRequest request);

    /**
     * 根据订单号查询订单备注信息
     *
     * @param request
     * @return
     */
    @ApiOperation("根据订单号查询订单备注信息")
    @PostMapping("/list")
    ApiResponse<List<OrderRemarkDto>> queryOrderRemark(@RequestBody @Valid QueryOrderRemarkRequest request);
}

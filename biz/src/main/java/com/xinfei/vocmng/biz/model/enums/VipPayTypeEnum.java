/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * VIP支付方式枚举
 *
 * <AUTHOR>
 * @version $ VipPayTypeEnum, v 0.1 2025/5/9 15:40 shaohui.chen Exp $
 */
@Getter
@AllArgsConstructor
public enum VipPayTypeEnum {
    
    WECHAT("wechat", "微信"),
    ALIPAY("alipay", "支付宝"),
    DEBIT_CARD("debit_card", "储蓄卡");
    
    /** 支付类型编码 */
    private final String code;
    
    /** 支付类型描述 */
    private final String desc;

    
    /**
     * 根据支付类型编码获取支付类型描述
     *
     * @param code 支付类型编码
     * @return 支付类型描述
     */
    public static String getDescByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        
        for (VipPayTypeEnum payType : VipPayTypeEnum.values()) {
            if (payType.getCode().equalsIgnoreCase(code)) {
                return payType.getDesc();
            }
        }
        
        return null;
    }
    
    /**
     * 根据支付类型编码获取支付类型枚举
     *
     * @param code 支付类型编码
     * @return 支付类型枚举
     */
    public static VipPayTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (VipPayTypeEnum payType : VipPayTypeEnum.values()) {
            if (payType.getCode().equals(code)) {
                return payType;
            }
        }
        
        return null;
    }
}

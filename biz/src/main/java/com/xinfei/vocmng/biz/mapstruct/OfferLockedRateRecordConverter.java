/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.vocmng.biz.rr.dto.OfferLockedRateRecordDto;
import com.xinfei.vocmng.dal.po.OfferLockedRateRecord;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ CommunicateSummaryConverter, v 0.1 2024-02-21 11:19 junjie.yan Exp $
 */
@Mapper
public interface OfferLockedRateRecordConverter {

    OfferLockedRateRecordConverter INSTANCE = Mappers.getMapper(OfferLockedRateRecordConverter.class);

    List<OfferLockedRateRecordDto> convert(List<OfferLockedRateRecord> offerLockedRateRecords);


}
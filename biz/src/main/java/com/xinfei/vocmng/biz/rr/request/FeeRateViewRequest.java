/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 费率查看请求
 *
 * <AUTHOR>
 * @version $ FeeRateViewRequest, v 0.1 2025-07-21 shaohui.chen Exp $
 */
@Data
public class FeeRateViewRequest {

    @ApiModelProperty(value = "借据订单号列表", required = true)
    @NotEmpty(message = "借据订单号列表不能为空")
    private List<LoanOrderInfo> loanOrderList;

    /**
     * 借据订单号信息
     */
    @Data
    public static class LoanOrderInfo {
        @ApiModelProperty(value = "借据号", required = true)
        private String loanNo;
        
        @ApiModelProperty(value = "订单号", required = true)
        private String orderNo;
    }
}

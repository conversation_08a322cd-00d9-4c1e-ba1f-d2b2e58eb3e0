package com.xinfei.vocmng.biz.component.remote;

import cn.hutool.core.collection.CollectionUtil;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/12/17
 */
@Component
public class RemoteProcessStrategy {
    private final Map<String, List<RemoteProcessor>> processMap = new HashMap<>();

    /**
     * 注册请求头构建处理类
     * @param appName
     * @param processor
     */
    public void register(String appName, RemoteProcessor processor){
        List<RemoteProcessor> processors = processMap.getOrDefault(appName, new ArrayList<>());
        processors.add(processor);
        processMap.put(appName,processors);
    }

    /**
     * 根据appName获取对应的请求构建处理类
     * @param appName
     * @return
     */
    public List<RemoteProcessor> getProcessor(String appName){
        return processMap.get(appName);
    }

    /**
     * 远程调用请求预处理
     * @param appName
     * @param template
     */
    public void doProcess(String appName, RequestTemplate template){
        List<RemoteProcessor> processorList = getProcessor(appName);
        if(CollectionUtil.isEmpty(processorList)){
            return;
        }

        for (RemoteProcessor processor : processorList){
            processor.process(template);
        }
    }
}

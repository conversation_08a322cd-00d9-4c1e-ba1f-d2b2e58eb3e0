package com.xinfei.vocmng.biz.model.enums;

/**
 * 服务响应状态码
 * <AUTHOR>
 * @create 2019/11/04
 */
public enum ResultCodeEnum {

    SUCCESS(200, "成功"),

    REPEAT(300, "重复操作"),

    FAILED(400,"失败"),
    NOT_LOGIN(401, "用户未登录"),
    PARAM_ILLEGAL(402,"参数非法"),
    ERROR_PATTERN(403,"参数格式不正确"),
    NOT_EXIST(404, "数据不存在"),
    DELETED(405,"已删除"),

    EXCEPTION(500,"异常"),
    ;

    private int code;
    private String msg;

    ResultCodeEnum(int code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static ResultCodeEnum getResultCode(int code) {
        for (ResultCodeEnum resultCodeEnum:ResultCodeEnum.values()){
            if (resultCodeEnum.getCode()==code){
                return resultCodeEnum;
            }
        }
        return null;
    }

    public static String getMessageByCode(Integer code) {
        for (ResultCodeEnum enums : ResultCodeEnum.values()) {
            if (enums.getCode() == code) {
                return enums.getMsg();
            }
        }
        return FAILED.getMsg();
    }
}

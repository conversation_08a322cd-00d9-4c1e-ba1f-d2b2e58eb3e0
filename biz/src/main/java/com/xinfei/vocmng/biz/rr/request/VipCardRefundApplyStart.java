/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/6/24 13:53
 * VipCardRefundApplyStart
 */
@Data
public class VipCardRefundApplyStart {

    @NotNull(message = "applyId不为空")
    private Long applyId;

    @ApiModelProperty("会员卡类型：1老会员卡  2新会员卡 3飞享会员 4飞跃会员")
    @NotNull(message = "会员卡类型type不为空")
    private Integer type;
}
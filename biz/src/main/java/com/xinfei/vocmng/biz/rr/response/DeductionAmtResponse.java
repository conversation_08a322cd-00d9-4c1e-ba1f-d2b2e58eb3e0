/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ ProfitDeductionReq, v 0.1 2024-03-28 22:15 junjie.yan Exp $
 */
@Data
public class DeductionAmtResponse {

    @ApiModelProperty("可抵扣金额下限")
    private BigDecimal amountLower;

    @ApiModelProperty("可抵扣金额上限")
    private BigDecimal amountUpper;
}
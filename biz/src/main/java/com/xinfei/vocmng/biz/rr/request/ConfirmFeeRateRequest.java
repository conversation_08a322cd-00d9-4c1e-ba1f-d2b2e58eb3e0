/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 确认费率请求
 *
 * <AUTHOR>
 * @version $ ConfirmFeeRateRequest, v 0.1 2025-07-21 shaohui.chen Exp $
 */
@Data
public class ConfirmFeeRateRequest {

    @ApiModelProperty(value = "借据号费率映射", required = true, notes = "key: 借据号, value: 固定费率（百分比形式，如24表示24%）")
    @NotEmpty(message = "借据号费率映射不能为空")
    private Map<String, BigDecimal> loanFeeRates;
}

package com.xinfei.vocmng.biz.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @version $ CallResultEnum, v 0.1 2025/3/7 10:34 shaohui.chen Exp $
 */
@AllArgsConstructor
@Getter
public enum CallResultEnum {
    CONNECT(1, "接通"),
    UN_CONNECT(2, "未接通"),
    OVERFLOW(3, "溢出"),
    NOT_CALL(4, "未拨打"),
    CALL_FAIL(5, "拨打失败");

    private final int code;      // 枚举对应的数值
    private final String desc;   // 枚举对应的描述
}

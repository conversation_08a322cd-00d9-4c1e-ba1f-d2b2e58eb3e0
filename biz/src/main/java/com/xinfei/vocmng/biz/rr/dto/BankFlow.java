/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ BankFlow, v 0.1 2024-05-10 15:43 junjie.yan Exp $
 */
@Data
public class BankFlow {

    @ApiModelProperty(value = "入账时间")
    private String transTime;

    @ApiModelProperty(value = "付款人账户")
    private String payAcc;

    @ApiModelProperty(value = "付款人名字")
    private String payName;

    @ApiModelProperty(value = "付款账号")
    private String payAccNo;

    @ApiModelProperty(value = "收款人账户")
    private String payeeAcc;

    @ApiModelProperty(value = "收款人名字")
    private String payeeName;

    @ApiModelProperty(value = "收款账号")
    private String payeeAccNo;

    @ApiModelProperty(value = "交易流水号")
    private String transNo;

    @ApiModelProperty(value = "流水金额/入账金额,(元)")
    private BigDecimal flowAmount;

    @ApiModelProperty(value = "可销账金额,(元)")
    private BigDecimal cancellableAmount;

    @ApiModelProperty(value = "已销账金额,(元)")
    private BigDecimal cancelledAmount;

    @ApiModelProperty(value = "已退款金额,(元)")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "还款方式code")
    private String channelCode;

    @ApiModelProperty(value = "还款方式描述")
    private String channelCodeDesc;
}
package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 合并队列管理接口
 *
 * <AUTHOR>
 * @version $ MergeQueueApi, v 0.1 2025/5/1 $
 */
@Api(tags = "合并队列管理接口")
@RequestMapping("/api/merge/queue")
public interface MergeQueueApi {

    /**
     * 获取队列状态
     *
     * @return 队列状态信息
     */
    @ApiOperation("获取队列状态")
    @GetMapping("/status")
    ApiResponse<Map<String, Object>> getQueueStatus();

    /**
     * 推送单个任务到队列
     *
     * @param webToken 要推送的webToken
     * @return 是否成功
     */
    @ApiOperation("推送单个任务到队列")
    @PostMapping("/push")
    ApiResponse<Boolean> pushTask(
            @ApiParam(value = "要推送的webToken", required = true) @RequestParam("webToken") String webToken);

    /**
     * 从UDesk导出客户并推送到队列
     *
     * @param filterId 客户过滤器ID
     * @param query 关键字搜索
     * @return 推送结果
     */
    @ApiOperation("从UDesk导出客户并推送到队列")
    @PostMapping("/push/batch")
    ApiResponse<Map<String, Object>> pushBatchTasks(
            @ApiParam(value = "客户过滤器ID") @RequestParam(value = "filterId", required = false) Long filterId,
            @ApiParam(value = "关键字搜索") @RequestParam(value = "query", required = false) String query);

    /**
     * 批量推送任务到队列
     *
     * @param webTokens 要推送的webToken列表
     * @return 推送结果
     */
    @ApiOperation("批量推送任务到队列")
    @PostMapping("/push/list")
    ApiResponse<Map<String, Integer>> pushTaskList(
            @ApiParam(value = "要推送的webToken列表", required = true) @RequestBody List<String> webTokens);

    /**
     * 清空队列
     *
     * @param queueType 队列类型：main, retry, processing, failed
     * @return 是否成功
     */
    @ApiOperation("清空队列")
    @DeleteMapping("/clear")
    ApiResponse<Boolean> clearQueue(
            @ApiParam(value = "队列类型：main, retry, processing, failed", required = true) @RequestParam("queueType") String queueType);

    /**
     * 获取失败任务列表
     *
     * @return 失败任务列表
     */
    @ApiOperation("获取失败任务列表")
    @GetMapping("/failed")
    ApiResponse<Map<String, Object>> getFailedTasks();

}

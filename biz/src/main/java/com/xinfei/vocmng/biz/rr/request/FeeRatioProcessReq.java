/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RepaymentProcessReq, v 0.1 2024-03-29 17:18 junjie.yan Exp $
 */
@Data
public class FeeRatioProcessReq {

    @ApiModelProperty("借据号")
    @NotBlank(message = "loanNo不能为空")
    private String loanNo;

    @ApiModelProperty("还款类型（1：非结清，2：结清）")
    @NotNull(message = "repayType不能为空")
    private Integer repayType;

    @ApiModelProperty("期数")
    private List<String> terms;

    @ApiModelProperty("目标费率")
    private BigDecimal targetFeeRatio;

    @ApiModelProperty("目标减免")
    private BigDecimal targetDeduct;
}
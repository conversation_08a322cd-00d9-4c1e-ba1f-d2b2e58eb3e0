/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.model.enums.SourceEnum;
import com.xinfei.vocmng.biz.model.enums.SummaryStatusEnum;
import com.xinfei.vocmng.biz.rr.CategoryNode;
import com.xinfei.vocmng.dal.mapper.DepartmentMapper;
import com.xinfei.vocmng.dal.mapper.EmployeeMapper;
import com.xinfei.vocmng.dal.mapper.IssueCategoryConfigMapper;
import com.xinfei.vocmng.dal.po.CommunicateSummary;
import com.xinfei.vocmng.dal.po.Department;
import com.xinfei.vocmng.dal.po.Employee;
import com.xinfei.vocmng.dal.po.IssueCategoryConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ EagleEyeCommunicateSummary, v 0.1 2025-04-09 14:59 junjie.yan Exp $
 */
@Component
public class EagleEyeCommunicateSummary {

    @Resource
    private EmployeeMapper employeeMapper;

    @Resource
    private DepartmentMapper departmentMapper;

    @Resource
    private IssueCategoryConfigMapper issueCategoryConfigMapper;


    public Map<String, Map<String, Integer>> countHourlyData(
            List<CommunicateSummary> nowCommunicateSummaries,
            List<CommunicateSummary> periodCommunicateSummaries) {

        Map<String, Map<String, Integer>> result = new LinkedHashMap<>();

        // 统计 now 的小时分布
        Map<String, Integer> nowHourCount = hourCount(nowCommunicateSummaries);
        result.put("nowDataList", nowHourCount);

        // 统计 period 的小时分布
        Map<String, Integer> periodHourCount = hourCount(periodCommunicateSummaries);
        result.put("periodDataList", periodHourCount);
        return result;
    }

    private Map<String, Integer> hourCount(List<CommunicateSummary> communicateSummaries) {
        Map<String, Integer> hourCount = new LinkedHashMap<>();
        for (int h = 0; h < 24; h++) {
            hourCount.put(String.valueOf(h), 0);
        }
        communicateSummaries.forEach(item -> {
            LocalDateTime time = item.getCreatedTime();
            if (time != null) {
                int hour = time.getHour();
                String key = String.valueOf(hour);
                hourCount.merge(key, 1, Integer::sum);
            }
        });

        return hourCount;
    }

    public Map<String, Map<String, Integer>> countSourceAndStatus(
            List<CommunicateSummary> nowCommunicateSummaries) {

        Map<String, Map<String, Integer>> result = new HashMap<>();

        // 统计 source
        Map<String, Integer> sourceCount = new HashMap<>();
        nowCommunicateSummaries.forEach(item -> {
            Integer source = item.getSource();
            if (source != null) {
                // 根据code获取枚举的name
                SourceEnum sourceEnum = SourceEnum.getByCode(source);
                if (sourceEnum != null) {
                    String key = sourceEnum.getName(); // 使用名称作为键
                    sourceCount.merge(key, 1, Integer::sum);
                }
            }
        });
        result.put("source", sourceCount);

        // 统计 status
        Map<String, Integer> statusCount = new HashMap<>();
        nowCommunicateSummaries.forEach(item -> {
            Integer status = item.getStatus();
            if (status != null) {
                // 根据code获取枚举的name
                SummaryStatusEnum statusEnum = SummaryStatusEnum.getByCode(status);
                if (statusEnum != null) {
                    String key = statusEnum.getName(); // 使用名称作为键
                    statusCount.merge(key, 1, Integer::sum);
                }
            }
        });
        result.put("status", statusCount);

        return result;
    }


    public Map<String, Map<String, Object>> countUserPerHour(
            List<CommunicateSummary> nowCommunicateSummaries) {

        Map<String, Map<String, Object>> result = new HashMap<>();

        // 1. 收集所有需要查询的 userIdentify
        Set<String> userIdentifies = new HashSet<>();
        nowCommunicateSummaries.forEach(item -> {
            String userId = item.getCreateUserIdentify();
            if (StringUtils.isNotBlank(userId)) {
                userIdentifies.add(userId);
            }
        });

        // 2. 批量查询员工信息
        List<Employee> employees = employeeMapper.selectByUserIdentifyIn(new ArrayList<>(userIdentifies));
        Map<String, Employee> employeeMap = employees.stream()
                .collect(Collectors.toMap(Employee::getUserIdentify, e -> e));

        // 3. 收集所有部门ID并查询部门名称
        Set<Long> departmentIds = employees.stream()
                .map(Employee::getDepartmentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<Department> departments = departmentMapper.getDepartmentNamesByIds(new ArrayList<>(departmentIds));
        Map<Long, Department> departmentMap = departments.stream()
                .collect(Collectors.toMap(Department::getId, d -> d));


        // 4. 统计小时并整合数据
        nowCommunicateSummaries.forEach(item -> {
            String userId = item.getCreateUserIdentify();
            LocalDateTime time = item.getCreatedTime();

            if (userId == null || time == null) {
                return;
            }

            int hour = time.getHour();
            String hourKey = String.valueOf(hour);

            // 初始化用户数据
            result.computeIfAbsent(userId, k -> {
                Employee employee = employeeMap.get(k);
                Long departmentId = (employee != null) ? employee.getDepartmentId() : null;
                Department department = departmentMap.get(departmentId);
                String departmentName = (department != null) ? department.getName() : "";

                Map<String, Object> userData = new HashMap<>();
                userData.put("hours", new HashMap<String, Integer>()); // 小时计数
                userData.put("name", employee != null ? employee.getName() : ""); // 员工姓名
                userData.put("departmentId", departmentId); // 部门ID
                userData.put("departmentName", departmentName); // 部门名称
                return userData;
            });

            // 更新小时计数
            Map<String, Integer> hours = (Map<String, Integer>) result.get(userId).get("hours");
            hours.merge(hourKey, 1, Integer::sum);
        });

        return result;
    }

    public List<Map<String, Object>> hierarchicalCategoryStats(
            List<CommunicateSummary> nowCommunicateSummaries,
            List<CommunicateSummary> periodCommunicateSummaries) {

        // 1. 构建层级结构并统计now数据的count1和小时分布
        Map<Long, CategoryNode> root1 = new HashMap<>();
        nowCommunicateSummaries.forEach(item -> {
            Long lv1 = item.getIssueCategoryLv1();
            Long lv2 = item.getIssueCategoryLv2();
            Long lv3 = item.getIssueCategoryLv3();
            LocalDateTime time = item.getCreatedTime();

            if (lv1 != null && time != null) {
                // 处理一级节点
                CategoryNode lv1Node = root1.computeIfAbsent(lv1, CategoryNode::new);
                lv1Node.incrementCount1();
                lv1Node.getHourlyCountNow().merge(String.valueOf(time.getHour()), 1, Integer::sum);

                // 处理二级节点
                if (lv2 != null) {
                    CategoryNode lv2Node = lv1Node.getSubCategories()
                            .computeIfAbsent(lv2, CategoryNode::new);
                    lv2Node.incrementCount1();
                    lv2Node.getHourlyCountNow().merge(String.valueOf(time.getHour()), 1, Integer::sum);

                    // 处理三级节点
                    if (lv3 != null) {
                        CategoryNode lv3Node = lv2Node.getSubCategories()
                                .computeIfAbsent(lv3, CategoryNode::new);
                        lv3Node.incrementCount1();
                        lv3Node.getHourlyCountNow().merge(String.valueOf(time.getHour()), 1, Integer::sum);
                    }
                }
            }
        });

        // 2. 统计period数据的count2和小时分布（仅存在于now结构的节点）
        periodCommunicateSummaries.forEach(item -> {
            Long lv1 = item.getIssueCategoryLv1();
            Long lv2 = item.getIssueCategoryLv2();
            Long lv3 = item.getIssueCategoryLv3();
            LocalDateTime time = item.getCreatedTime();

            if (lv1 != null && time != null) {
                // 查找一级节点（仅统计存在于now结构的节点）
                CategoryNode lv1Node = root1.get(lv1);
                if (lv1Node != null) {
                    lv1Node.incrementCount2();
                    lv1Node.getHourlyCountPeriod().merge(String.valueOf(time.getHour()), 1, Integer::sum);

                    // 查找二级节点
                    if (lv2 != null) {
                        CategoryNode lv2Node = lv1Node.getSubCategories().get(lv2);
                        if (lv2Node != null) {
                            lv2Node.incrementCount2();
                            lv2Node.getHourlyCountPeriod().merge(String.valueOf(time.getHour()), 1, Integer::sum);

                            // 查找三级节点
                            if (lv3 != null) {
                                CategoryNode lv3Node = lv2Node.getSubCategories().get(lv3);
                                if (lv3Node != null) {
                                    lv3Node.incrementCount2();
                                    lv3Node.getHourlyCountPeriod().merge(String.valueOf(time.getHour()), 1, Integer::sum);
                                }
                            }
                        }
                    }
                }
            }
        });

        // 3. 收集所有分类ID
        Set<Long> categoryIds = new HashSet<>();
        // 收集now的分类ID
        nowCommunicateSummaries.forEach(item -> {
            Long lv1 = item.getIssueCategoryLv1();
            Long lv2 = item.getIssueCategoryLv2();
            Long lv3 = item.getIssueCategoryLv3();
            if (lv1 != null) {
                categoryIds.add(lv1);
            }
            if (lv2 != null) {
                categoryIds.add(lv2);
            }
            if (lv3 != null) {
                categoryIds.add(lv3);
            }
        });
        // 收集period的分类ID（可选，根据需求决定是否需要）
        periodCommunicateSummaries.forEach(item -> {
            Long lv1 = item.getIssueCategoryLv1();
            Long lv2 = item.getIssueCategoryLv2();
            Long lv3 = item.getIssueCategoryLv3();
            if (lv1 != null) {
                categoryIds.add(lv1);
            }
            if (lv2 != null) {
                categoryIds.add(lv2);
            }
            if (lv3 != null) {
                categoryIds.add(lv3);
            }
        });

        // 4. 查询分类名称
        List<IssueCategoryConfig> categories = issueCategoryConfigMapper.selectByIds(new ArrayList<>(categoryIds));
        if (categoryIds.contains(-1L)) {
            IssueCategoryConfig config = new IssueCategoryConfig();
            config.setName("待补充");
            config.setId(-1L);
            categories.add(config);
        }
        Map<Long, String> categoryNames = categories
                .stream()
                .collect(
                        Collectors.toMap(IssueCategoryConfig::getId, config -> Optional.ofNullable(config.getName()).orElse("待补充"))
                );

        // 5. 排序并转换为JSON
        List<CategoryNode> sortedRoot = new ArrayList<>(root1.values());
        sortedRoot.sort((a, b) -> Integer.compare(b.getCount1(), a.getCount1()));

        List<Map<String, Object>> jsonResult = new ArrayList<>();
        for (CategoryNode node : sortedRoot) {
            jsonResult.add(convertToJSON(node, categoryNames));
        }

        return jsonResult;
    }

    // 修改后的JSON转换方法
    private static Map<String, Object> convertToJSON(CategoryNode node, Map<Long, String> categoryNames) {
        Map<String, Object> jsonNode = new HashMap<>();
        jsonNode.put("id", String.valueOf(node.getCategoryId()));
        jsonNode.put("name", categoryNames.get(node.getCategoryId()));
        jsonNode.put("count1", node.getCount1());
        jsonNode.put("count2", node.getCount2());
        jsonNode.put("hourlyNow", node.getHourlyCountNow()); // 新增
        jsonNode.put("hourlyPeriod", node.getHourlyCountPeriod()); // 新增

        // 递归处理子节点并排序
        List<CategoryNode> subNodes = new ArrayList<>(node.getSubCategories().values());
        subNodes.sort((a, b) -> Integer.compare(b.getCount1(), a.getCount1())); // 按count1降序排序

        // 递归处理子节点
        List<Map<String, Object>> children = new ArrayList<>();
        for (CategoryNode child : subNodes) {
            children.add(convertToJSON(child, categoryNames));
        }
        jsonNode.put("children", children);

        return jsonNode;
    }


}
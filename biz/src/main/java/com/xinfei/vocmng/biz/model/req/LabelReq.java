/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import com.xinfei.vocmng.biz.model.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ LabelReq, v 0.1 2023/12/20 20:39 wancheng.qu Exp $
 */
@Data
public class LabelReq extends PageRequestDto implements Serializable {

    @ApiModelProperty(value = "标签id,修改删除用")
    private Long labelId;

    @ApiModelProperty(value = "标签类型id，多选")
    private List<Long> categoryId;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "标签名称")
    private String name;

    @ApiModelProperty(value = "标签颜色")
    private String color;

    @ApiModelProperty(value = "标签方案内容")
    private String solution;

    @ApiModelProperty(value = "标签类型")
    private String category;

    @ApiModelProperty(value = "标签id集合，适用于通过标签名多选框")
    private List<Long> ids;

    @ApiModelProperty(value = "1新增标签类型2已有标签类型")
    private Integer type;

    @ApiModelProperty(value = "标签类型id,新增用")
    private Long id;

    @ApiModelProperty(value = "是否删除：1已删除 0未删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "标签可选状态：1可选 0不可选")
    private Integer displayState;


}
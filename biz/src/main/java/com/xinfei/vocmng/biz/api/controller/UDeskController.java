/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.vocmng.biz.api.UDeskApi;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.log.DigestLogAnnotated;
import com.xinfei.vocmng.biz.model.req.SetComSummaryRequest;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.remote.NPayService;
import com.xinfei.vocmng.biz.service.UDeskService;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.vocmng.itl.rr.PublicAccountInfo;
import com.xinfei.vocmng.itl.rr.PublicAccountRequest;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version $ UDeskController, v 0.1 2024-02-04 11:39 junjie.yan Exp $
 */

@Slf4j
@RestController
public class UDeskController implements UDeskApi {

    @Autowired
    private UDeskService uDeskService;

    @Resource
    private RedisUtils redisUtils;

    private static final String redisKey = "callId";

    @Autowired
    private VocConfig vocConfig;

    @Resource
    private NPayService nPayService;


    @Override
    @DigestLogAnnotated("setComSummary")
    public ApiResponse<String> setComSummary(SetComSummaryRequest request) {
        log.info("uDesk接通" + JsonUtil.toJson(request));

        if (!vocConfig.getIsUDesk()) {
            return ApiResponse.success("udesk开关已关");
        }

        if (StringUtils.isEmpty(request.getCall_id()) || StringUtils.isEmpty(request.getCustomer_phone()) ||
                StringUtils.isEmpty(request.getAgent_id()) || StringUtils.isEmpty(request.getDisplay_number()) ||
                StringUtils.isEmpty(request.getWorkflow()) || StringUtils.isEmpty(request.getCall_result()) ||
                StringUtils.isEmpty(request.getOccasion())) {
            return ApiResponse.fail("uDeskError-uDesk传参不合法");
        }

//        if (!"in".equals(request.getWorkflow()) || !"客服接听".equals(request.getCall_result())) {
//            return ApiResponse.fail("不是呼入类型/客服接听");
//        }

        //仅接收呼入、呼出、挂断事件
        if (!request.getOccasion().equals("agent_incall") && !request.getOccasion().equals("hangup") && !request.getOccasion().equals("customer_answer")) {
            return ApiResponse.fail("不是客服接听/挂断");
        }

        //针对 callId+事件类型 做幂等
        try {
            while (!redisUtils.setnx(redisKey, "callId", 2L, TimeUnit.SECONDS)) {
                TimeUnit.MILLISECONDS.sleep(300);
            }

            if (redisUtils.get(request.getCall_id() + request.getOccasion()) != null) {
                return ApiResponse.fail("此call_id + occasion已被使用");
            }
            redisUtils.set(request.getCall_id() + request.getOccasion(), request.getCall_id(), 1, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("redis setnx error");
        } finally {
            redisUtils.del(redisKey);
        }

        if (request.getOccasion().equals("agent_incall")) {
            log.info(LogUtil.infoLog("agent_incall_呼入"));
        }
        if (request.getOccasion().equals("customer_answer")) {
            log.info(LogUtil.infoLog("customer_answer_呼出"));
        }
        if (request.getOccasion().equals("hangup")) {
            log.info(LogUtil.infoLog("hangup_挂断"));
        }

        String name = (request.getOccasion().equals("agent_incall") || request.getOccasion().equals("customer_answer")) ? "start" : "end";
        String result = uDeskService.setComSummary(request, name);

        if (StringUtils.isEmpty(result)) {
            return ApiResponse.success(result);
        } else {
            return ApiResponse.fail(result);
        }
    }

    @Override
    public ApiResponse<Boolean> sessionsSync(String startTime,String endTime) {
        uDeskService.sessionsSync(startTime,endTime);
        return ApiResponse.success(Boolean.TRUE);

    }

    @Override
    public ApiResponse<Boolean> callSync(String startTime,String endTime) {
        uDeskService.callSync(startTime,endTime);
        return ApiResponse.success(Boolean.TRUE);
    }

    @Override
    public ApiResponse<Boolean> robotSync(String startTime,String endTime) {
        uDeskService.robotSync(startTime,endTime);
        return ApiResponse.success(Boolean.TRUE);    }

    @Override
    public ApiResponse<PublicAccountInfo> queryPublicAccountInfo(PublicAccountRequest request) {
        return ApiResponse.success(nPayService.queryPublicAccountInfo(request));
    }

    @Override
    public ApiResponse<Boolean> robotDetailsSync(String startTime,String endTime) {
        uDeskService.robotDetailsSync("uDeskRobotDetailsSync", startTime, endTime);
        return ApiResponse.success(Boolean.TRUE);
    }

    @Override
    public ApiResponse<Boolean> uDeskSyncAll(String startTime,String endTime) {
        uDeskService.uDeskSyncAll("uDeskSync", startTime, endTime);
        return ApiResponse.success(Boolean.TRUE);
    }
}
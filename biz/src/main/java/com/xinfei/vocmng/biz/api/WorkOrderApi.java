package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.WorkOrderDetailDto;
import com.xinfei.vocmng.biz.rr.dto.WorkOrderSimpleDto;
import com.xinfei.vocmng.biz.rr.request.QueryWorkOrderRequest;
import com.xinfei.vocmng.biz.rr.request.QueryWorkOrderUrlRequest;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version $ WorkOrderApi, v 0.1 2024/1/15 19:51 qu.lu Exp $
 */
@Api(tags = "工单记录相关接口")
@RequestMapping("/work/order")
public interface WorkOrderApi {
    /**
     * 根据订单号查询订单明细信息
     *
     * @param request
     * @return
     */
    @PostMapping("/list")
    ApiResponse<PageResultResponse<WorkOrderDetailDto>> queryWorkOrderDetail(@RequestBody @Valid QueryWorkOrderRequest request);

    /**
     * 根据会话小结查询工单URL
     *
     * @param request
     * @return
     */
    @PostMapping("/url")
    ApiResponse<WorkOrderSimpleDto> loadWorkOrderUrl(@RequestBody @Valid QueryWorkOrderUrlRequest request);
}

package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.contractcore.common.service.facade.vo.ContractVO;
import com.xinfei.vocmng.biz.api.ContractApi;
import com.xinfei.vocmng.biz.config.LoginUserConfig;
import com.xinfei.vocmng.biz.log.DigestLogAnnotated;
import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.remote.ContractRemoteService;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.dal.po.CapitalMail;
import com.xinfei.vocmng.dal.po.SendMail;
import com.xinfei.vocmng.itl.rr.ContractStatusDetail;
import com.xinfei.vocmng.itl.rr.LoanInfoRequest;
import com.xinfei.vocmng.itl.rr.ProtocolDto;
import com.xinfei.vocmng.itl.rr.SettlementCertificateReq;
import com.xinfei.vocmng.itl.rr.dto.ContractBaseDataDto;
import com.xinfei.vocmng.itl.rr.dto.ContractDataDto;
import com.xinfei.vocmng.itl.rr.dto.ContractDetailDto;
import com.xinfei.vocmng.biz.rr.dto.DocumentRecordDto;
import com.xinfei.vocmng.itl.rr.dto.LoanInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ ContractController, v 0.1 2023/12/21 20:18 qu.lu Exp $
 */
@Slf4j
@RestController
@LoginRequired
public class ContractController implements ContractApi {

    @Autowired
    private ContractRemoteService contractRemoteService;

    @Resource
    private LoginUserConfig loginUserConfig;

    @Override
    @DigestLogAnnotated("queryContractDetailList")
    public ApiResponse<List<ContractDetailDto>> queryContractDetailList(QueryContractListRequest request) {
        return contractRemoteService.queryCashSubContractList(request);
    }

    @Override
    @DigestLogAnnotated("queryContractBaseData")
    public ApiResponse<ContractBaseDataDto> queryContractBaseData() {
        return ApiResponse.success(loginUserConfig.getContractBaseDataDto());
    }

    @Override
    @DigestLogAnnotated("queryJqzmInfo")
    public ApiResponse<LoanInfoDto> queryJqzmInfo(LoanInfoRequest request) {
        return contractRemoteService.queryJqzmInfo(request);
    }

    @Override
    @DigestLogAnnotated("querySpecialList")
    public ApiResponse<List<String>> querySpecialList() {
        return contractRemoteService.querySpecialList();
    }

    @Override
    @DigestLogAnnotated("queryObtainStatus")
    public ApiResponse<List<ContractStatusDetail>> queryObtainStatus(List<String> orderIds) {
        return contractRemoteService.queryObtainStatus(orderIds);
    }

    @Override
    @DigestLogAnnotated("sendMail")
    public ApiResponse<Boolean> sendMail(SettlementCertificateReq req) {
        return contractRemoteService.sendMail(req);
    }

    @Override
    @DigestLogAnnotated("sendIntermediaryAgreement")
    public ApiResponse<Boolean> sendIntermediaryAgreement(SettlementCertificateReq req) {
        return contractRemoteService.sendIntermediaryAgreement(req);
    }

    @Override
    @DigestLogAnnotated("downFile")
    public ApiResponse<List<String>> downFile(SettlementCertificateReq req) {
        return contractRemoteService.downFile(req);
    }

    @Override
    @DigestLogAnnotated("applyNow")
    public ApiResponse<String> applyNow(SettlementCertificateReq req) {
        return contractRemoteService.applyNow(req);
    }

    @Override
    @DigestLogAnnotated("queryApplyList")
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<PageResultResponse<DocumentRecordDto>> queryApplyList(DocumentRecordReq record) {
        return contractRemoteService.queryApplyList(record);
    }

    @Override
    @DigestLogAnnotated("lookFile")
    public ApiResponse<LookFileResp> lookFile(DocumentRecordReq record) {
        return contractRemoteService.lookFile(record);
    }

    @Override
    @DigestLogAnnotated("sendFile")
    public ApiResponse<String> sendFile(DocumentRecordReq record) {
        return contractRemoteService.sendFile(record);
    }

    @Override
    @DigestLogAnnotated("uploadDocumentRecord")
    public ApiResponse<Boolean> uploadDocumentRecord(MultipartFile[] file, String mail,Integer type, Long id) {
        return contractRemoteService.uploadDocumentRecord(file, mail,type, id);
    }

    @Override
    @DigestLogAnnotated("queryCapitalList")
    public ApiResponse<List<CapitalMail>> queryCapitalList() {
        return contractRemoteService.queryCapitalList();
    }

    @Override
    @DigestLogAnnotated("updateCapital")
    public ApiResponse<Boolean> updateCapital(CapitalMail req) {
        return contractRemoteService.updateCapital(req);
    }

    @Override
    @DigestLogAnnotated("insertCapital")
    public ApiResponse<Boolean> insertCapital(CapitalMail req) {
        return contractRemoteService.insertCapital(req);
    }

    @Override
    @DigestLogAnnotated("querySendList")
    public ApiResponse<List<SendMail>> querySendList(SendMail req) {
        return contractRemoteService.querySendList(req);
    }

    @Override
    @DigestLogAnnotated("insertSend")
    public ApiResponse<Boolean> insertSend(SendMail req) {
        return contractRemoteService.insertSend(req);
    }

    @Override
    @DigestLogAnnotated("updateSend")
    public ApiResponse<Boolean> updateSend(SendMail req) {
        return contractRemoteService.updateSend(req);
    }

    @Override
    @DigestLogAnnotated("queryContractList")
    public ApiResponse<List<ContractDataDto>> queryContractList(ContractReq req) {
        return contractRemoteService.queryContractList(req);
    }

    @Override
    @DigestLogAnnotated("getProtocolsByType")
    public ApiResponse<List<ProtocolDto>> getProtocolsByType(ContractReq req) {
        return contractRemoteService.getProtocolsByType(req);
    }

    @Override
    @DigestLogAnnotated("resignContract")
    public ApiResponse<String> resignContract(ResignContractReq req) {
        return contractRemoteService.resignContract(req);
    }

    @Override
    @DigestLogAnnotated("queryVipContractList")
    public ApiResponse<List<ContractVO>> queryContractCoreList(ContractCoreReq req) {
        return contractRemoteService.queryContractCoreList(req);
    }
}

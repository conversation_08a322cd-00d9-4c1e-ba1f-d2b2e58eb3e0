/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RefundTrialReq, v 0.1 2024-05-20 15:39 junjie.yan Exp $
 */
@Data
public class RefundTrialReq {

    @ApiModelProperty(value = "订单号")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @ApiModelProperty(value = "还款单号列表")
    private List<String> repaymentNos;

    @ApiModelProperty("订单类型，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）")
    @NotBlank(message = "订单类型不能为空")
    private String orderType;

    @ApiModelProperty(value = "借据号")
    @NotBlank(message = "借据号不能为空")
    private String loanNo;

    @ApiModelProperty(value = "账单号")
    private String billNo;

    @ApiModelProperty("退款方式,ONLINE_REFUND:线上原路原退,OFFLINE_REFUND:线下退款")
    @NotBlank(message = "退款方式不能为空")
    private String refundType;

    @ApiModelProperty("总退款金额（元）")
    @NotNull(message = "总退款金额不能为空")
    private BigDecimal totalRefundAmt;

    @ApiModelProperty("是否审批")
    @NotNull(message = "是否审批必填")
    private Boolean isReview;
}
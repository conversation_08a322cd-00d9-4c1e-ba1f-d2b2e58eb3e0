/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> 2024/7/24 下午1:47
 * RepayMethodRequest
 */
@Data
public class RepayMethodRequest{

    @ApiModelProperty("方案编号")
    private Long planId;

    @ApiModelProperty("方案明细编号")
    private String planDetailId;

    @ApiModelProperty("还款方式（1：app自助，2：系统代扣，3：聚合支付，4：线下还款）")
    @NotNull(message = "还款方式不能为空")
    private Integer repayMethod;

    @ApiModelProperty("方案类型：1：还当期，2：提前结清")
    private Integer planType;

    @ApiModelProperty("银行卡id 系统代扣必传")
    private String bankCardId;

    @ApiModelProperty("方案來源")
    @NotBlank(message = "方案來源不能为空")
    private String planSource;

    @ApiModelProperty("手机号 聚合支付，线下还款必传")
    private String mobile;

    @ApiModelProperty("是否发送短信 聚合支付，线下还款必传")
    private Boolean isSendSms;

    @ApiModelProperty("用户号")
    @NotBlank(message = "userNo不能为空")
    private String userNo;

    @ApiModelProperty("客户号")
    @NotBlank(message = "客户号不能为空")
    private String custNo;

    @ApiModelProperty("修改明细")
    @NotEmpty
    private List<RepayMethodDetail> repayMethodDetails;

    @ApiModelProperty("方案有效期")
    private String endTime;
}
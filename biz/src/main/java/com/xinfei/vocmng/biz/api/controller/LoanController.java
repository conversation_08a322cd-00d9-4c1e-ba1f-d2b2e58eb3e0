/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.google.gson.Gson;
import com.xinfei.huttalegal.facade.rr.res.LegalAgencyDetail;
import com.xinfei.vocmng.biz.api.LoanApi;
import com.xinfei.vocmng.biz.log.DigestLogAnnotated;
import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.DiversionOrderDto;
import com.xinfei.vocmng.biz.rr.dto.OrderDto;
import com.xinfei.vocmng.biz.rr.dto.OrderFilterFactorDto;
import com.xinfei.vocmng.biz.rr.dto.RepaymentsDto;
import com.xinfei.vocmng.biz.rr.dto.bill.LoanPlanDto;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.CanRepayResponse;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.biz.service.FundCoreService;
import com.xinfei.vocmng.biz.service.LoanService;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.vocmng.itl.client.feign.impl.LcsFeignService;
import com.xinfei.vocmng.itl.rr.ProductDetailInfo;
import com.xinfei.vocmng.itl.rr.QueryLoanDetailResp;
import com.xinfei.vocmng.itl.rr.QueryLoanReq;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ OrderBillController, v 0.1 2023-12-14 14:47 junjie.yan Exp $
 */
@RestController
@LoginRequired
public class LoanController implements LoanApi {

    @Resource
    private LoanService loanService;

    @Resource
    private FundCoreService fundCoreService;

    @Resource
    private RedisUtils redisUtils;

    @Autowired
    private LcsFeignService lcsFeignClient;

    @Override
    @DigestLogAnnotated("getOrderList")
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<Paging<OrderDto>> getOrderList(@RequestBody GetOrderListRequest request) {
        if (CollectionUtils.isEmpty(request.getUserNos())
                && StringUtils.isEmpty(request.getMobile())
                && StringUtils.isEmpty(request.getIdCard())
                && CollectionUtils.isEmpty(request.getOrderNos())
                && CollectionUtils.isEmpty(request.getLoanNos())
                && StringUtils.isEmpty(request.getOutOrderNumber())
                && StringUtils.isEmpty(request.getChannelOrderNumber())
                && StringUtils.isEmpty(request.getMobileCust())
                && StringUtils.isEmpty(request.getCustNo())
                && StringUtils.isEmpty(request.getThirdLoanNo())
        ) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "手机号/身份证号/用户号/订单号/资方订单号/渠道订单号/借据号/资方借据号 必传其一");
        }

        if (!CollectionUtils.isEmpty(request.getLoanStatus()) && !StringUtils.isEmpty(request.getMobile()) && !StringUtils.isEmpty(request.getIdCard())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "借据状态作为筛选条件，手机号和身份证号必传其一");
        }

        String orderNoByOutOrderNumber = "";
        if (request.getOutOrderNumber() != null) {
            orderNoByOutOrderNumber = loanService.queryByOutOrderNumber(request);
            if (StringUtils.isEmpty(orderNoByOutOrderNumber)) {
                throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "此资方订单号查不到订单");
            }
        }

        String orderNoByChannelOrderNumber = "";
        if (request.getChannelOrderNumber() != null) {
            orderNoByChannelOrderNumber = loanService.queryByChannelOrderNumber(request);
            if (StringUtils.isEmpty(orderNoByChannelOrderNumber)) {
                throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "此渠道订单号查不到订单");
            }
        }

        if (StringUtils.isNotEmpty(orderNoByChannelOrderNumber) && StringUtils.isNotEmpty(orderNoByOutOrderNumber) && !orderNoByOutOrderNumber.equals(orderNoByChannelOrderNumber)) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "通过资方订单号与渠道订单号查询的订单号不一致");
        }

        if (StringUtils.isNotEmpty(orderNoByOutOrderNumber)) {
            request.setOrderNos(Collections.singletonList(orderNoByOutOrderNumber));
        }

        if (StringUtils.isNotEmpty(orderNoByChannelOrderNumber)) {
            request.setOrderNos(Collections.singletonList(orderNoByChannelOrderNumber));
        }

        if (request.getIsCustomerDetail() == null) {
            request.setIsCustomerDetail(false);
        }

        if (StringUtils.isNotBlank(request.getThirdLoanNo())) {
            List<String> loanNosFromThirdLoanNo = new ArrayList<>();
            QueryLoanReq queryLoanReq = new QueryLoanReq();
            queryLoanReq.setThirdLoanNo(request.getThirdLoanNo());
            List<QueryLoanDetailResp> queryResult = lcsFeignClient.queryLoanInfo(queryLoanReq);

            if (CollectionUtil.isNotEmpty(queryResult)) {
                loanNosFromThirdLoanNo = queryResult.stream()
                        .map(item -> item.getLoanInfo().getLoanNo())
                        .filter(Objects::nonNull).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(loanNosFromThirdLoanNo)) {
                throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "此资方借据号查不到订单");
            }
            request.setLoanNos(loanNosFromThirdLoanNo);
        }

        return ApiResponse.success(loanService.queryOrderList(request));
    }

    @Override
    @DigestLogAnnotated("getOrderDetail")
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<OrderDto> getOrderDetail(GetOrderDetailRequest request) {
        return ApiResponse.success(loanService.queryOrderDetail(request));
    }

    @Override
    public ApiResponse<Boolean> orderCancel(OrderCancelRequest request) {
        return ApiResponse.success(loanService.orderCancel(request.getOrderNo()));
    }

    @Override
    @DigestLogAnnotated("getBillList")
    public ApiResponse<List<LoanPlanDto>> getBillList(GetBillListRequest request) {
        if (CollectionUtils.isEmpty(request.getLoanNos())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "借据号必填");
        }
        return ApiResponse.success(loanService.queryBillList(request));
    }

    @Override
    @DigestLogAnnotated("getBillListWithPaging")
    public ApiResponse<Paging<LoanPlanDto>> getBillListWithPaging(GetBillListPageRequest request) {
        if (CollectionUtils.isEmpty(request.getLoanNos())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "借据号必填");
        }
        return ApiResponse.success(loanService.queryBillListWithPaging(request));
    }

    @Override
    public ApiResponse<CanRepayResponse> canRepay(CanRepayRequest request) {
        if (StringUtils.isEmpty(request.getLoanNo())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "借据号必填");
        }

        if (StringUtils.isEmpty(request.getLoanStatus()) || "FP".equals(request.getLoanStatus())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "借据状态不能为 结清 借据");
        }

        return ApiResponse.success(loanService.queryCanRepay(request));
    }


    @Override
    @DigestLogAnnotated("getRepayments")
    public ApiResponse<Paging<RepaymentsDto>> getRepayments(GetRepaymentsRequest request) {
        if (CollectionUtils.isEmpty(request.getLoanNo())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "借据号必填");
        }

        return ApiResponse.success(loanService.queryRepayments(request));
    }

    @Override
    @DigestLogAnnotated("logoutQuota")
    public ApiResponse<Boolean> logoutQuota(LogoutQuotaRequest request) {
        return ApiResponse.success(fundCoreService.logoutQuota(request));
    }

    @Override
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<Paging<DiversionOrderDto>> getApiOrderList(ApiOrderRequest apiOrderRequest) {
        return ApiResponse.success(loanService.queryApiOrderList(apiOrderRequest));
    }

    @Override
    public ApiResponse<List<ProductDetailInfo>> queryProductList() {
        return ApiResponse.success(loanService.queryProductList());
    }

    @Override
    public ApiResponse<LegalAgencyDetail> queryAgencyDetail(AgencyDetailRequest agencyDetailRequest) {
        return ApiResponse.success(loanService.queryAgencyDetail(agencyDetailRequest));
    }

    @Override
    public void orderListDownload(HttpServletResponse response, GetBillListRequest request) {
        loanService.orderListDownload(response, request);
    }

    @Override
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<OrderFilterFactorDto> getOrderFilterFactor(OrderFilterFactorRequest request) {
        OrderFilterFactorDto orderFilterFactorDto = new OrderFilterFactorDto();
        if (StringUtils.isNotBlank(request.getCustNo())) {
            Gson gson = new Gson();
            // 从 Redis 获取数据并解析为 orderFilterFactorDto
            orderFilterFactorDto = gson.fromJson(redisUtils.get(request.getCustNo()), OrderFilterFactorDto.class);
        }
        return ApiResponse.success(orderFilterFactorDto);
    }
}
/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryResp;
import com.xinfei.vocmng.dal.po.CommunicateSummary;
import com.xinfei.vocmng.itl.rr.GetTaskByMobileResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ CommunicateSummaryDetailReq, v 0.1 2024-02-04 14:54 junjie.yan Exp $
 */
@Data
public class CommunicateSummaryDetailRsp {

    @ApiModelProperty(value = "中继号名称")
    private String displayNumberName;

    @ApiModelProperty(value = "新建的会话小结")
    private CommunicateSummary communicateSummary;

    @ApiModelProperty(value = "最近5条会话小结")
    private List<CommunicateSummaryResp> communicateSummaries;

    @ApiModelProperty(value = "最近5条工单")
    private List<GetTaskByMobileResp> tasks;

}
/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ BillPlanDetailDto, v 0.1 2024-03-11 14:07 junjie.yan Exp $
 */
@Data
public class PlanDetailDto {
    @ApiModelProperty("本金")
    private BigDecimal prinAmt;

    @ApiModelProperty("利息")
    private BigDecimal intAmt;

    @ApiModelProperty("担保费")
    private BigDecimal fee1Amt;

    @ApiModelProperty("反担保费")
    private BigDecimal fee2Amt;

    @ApiModelProperty("罚息")
    private BigDecimal ointAmt;

    @ApiModelProperty("罚息（我方）")
    private BigDecimal fee3Amt;

    @ApiModelProperty("提前结清手续费")
    private BigDecimal fee4Amt;

    @ApiModelProperty("催费")
    private BigDecimal fee6Amt;

    @ApiModelProperty("金额和")
    private BigDecimal sumAmt;

    @ApiModelProperty("voc担保费=担保费+反担保费")
    private BigDecimal guaranteeFee;

    @ApiModelProperty("voc逾期费=罚息+罚息（我方）+催费")
    private BigDecimal lateFee;

    @ApiModelProperty("资方")
    private String fundSource;

    @ApiModelProperty("本息罚不能减免资方名单")
    private List<String> fundSourceWhiteList;

}
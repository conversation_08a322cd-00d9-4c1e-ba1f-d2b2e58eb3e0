package com.xinfei.vocmng.biz.model.enums;

import com.xinfei.vocmng.biz.model.base.ControlItemValue;
import com.xinfei.vocmng.biz.model.base.ControlRes;
import com.xinfei.vocmng.biz.service.CostCalculationStrategy;
import com.xinfei.vocmng.biz.service.impl.*;


/** 费控类型对应的枚举项
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
public enum ControlEnum {
    GUARANTEE_REDUCTION(1, GuaranteeReductionStrategy.class,"担保费减免额度"),
    PENALTY_REDUCTION(2, PenaltyReductionStrategy.class,"逾期费减免额度"),
    PRINCIPAL_AMOUNT(3, PrinReductionStrategy.class,"本金额度"),
    INTEREST_AMOUNT(4, IntReductionStrategy.class,"利息额度"),
    PREPAYMENT_FEE(5, RedReductionStrategy.class,"提前结清费"),
    DEDUCTION_AMOUNT(6, DeductionAmtStrategy.class,"抵扣额度"),
    DEDUCTION_POOL_RANGE(7, PenaltyReductionStrategy.class,"抵扣池范围"),
    PROFIT_PRINCIPAL_REDUCTION(8, PrinReductionStrategy.class,"营收订单本金减免额度"),
    VIP_CARD_REFUND_AMOUNT(9, DeductionStrategy.class, "会员卡退费"),
    RENEW_CARD_REFUND_AMOUNT(10, DeductionStrategy.class, "飞享会员退费");


    private final Integer controlChildType;
    private final Class<? extends CostCalculationStrategy> strategyClass;
    private final String comment;
    private CostCalculationStrategy strategy;

    ControlEnum(int controlChildType, Class<? extends CostCalculationStrategy> strategyClass,String comment) {
        this.controlChildType = controlChildType;
        this.strategyClass = strategyClass;
        this.comment=comment;
    }

    public ControlRes<?,?> calculateAmount(ControlItemValue<?, ?, ?> values, Object... params) {
        if (strategy == null) {
            try {
                strategy = strategyClass.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                throw new RuntimeException("Failed to instantiate strategy: " + e.getMessage(), e);
            }
        }
        return strategy.calculateAmount(values, params);
    }

    public static ControlEnum getByControlChildType(int controlChildType) {
        for (ControlEnum type : ControlEnum.values()) {
            if (type.controlChildType == controlChildType) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid control_child_type: " + controlChildType);
    }

    public static String getCommentByControlChildType(int controlChildType) {
        for (ControlEnum type : ControlEnum.values()) {
            if (type.controlChildType == controlChildType) {
                return type.comment;
            }
        }
        throw new IllegalArgumentException("Invalid control_child_type: " + controlChildType);
    }
}

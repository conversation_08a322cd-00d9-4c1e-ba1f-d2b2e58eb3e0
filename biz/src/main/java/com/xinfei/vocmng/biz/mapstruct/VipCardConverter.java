/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.supervip.common.enums.*;
import com.xinfei.supervip.interfaces.model.admin.dto.*;
import com.xinfei.vipcore.facade.rr.dto.*;
import com.xinfei.vipcore.facade.rr.request.VipRefundApplyRequest;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.biz.rr.request.VipCardRefundApply;
import com.xinfei.vocmng.biz.rr.response.CardRefund;
import com.xinfei.vocmng.itl.model.enums.VipCardStatusEnum;
import com.xinfei.vocmng.itl.rr.MemberCard;
import com.xinfei.vocmng.itl.rr.OldMemberCard;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $ CustomerConverter, v 0.1 2023-12-20 19:57 junjie.yan Exp $
 */
@Mapper
public interface VipCardConverter {

    VipCardConverter INSTANCE = Mappers.getMapper(VipCardConverter.class);

    default LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime();
    }

    default LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDate();
    }

    default String formatLocalDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(dateTime);
    }

    @Mapping(source = "vipName", target = "cardName")
    @Mapping(source = "userNo", target = "userId")
    @Mapping(source = "startAt", target = "beginTime")
    @Mapping(source = "endAt", target = "endTime")
    @Mapping(source = "payPrice", target = "payAmount")
    @Mapping(source = "id", target = "cardId")
    @Mapping(source = "orderNumber", target = "orderNo")
    @Mapping(source = "startAt", target = "createTime")
    @Mapping(source = "status", target = "vipStatus", qualifiedByName = "renewStatus")
    MemberCardDto renewLogAdminDtoToMemberCardDto(RenewLogAdminDto renewLogAdminDto);

    @Named("renewStatus")
    static String renewStatus(Integer status) {
        if (status == null) {
            return null;
        }

        return VipCardStatusEnum.fromRenewCardStatus(status).orElse(VipCardStatusEnum.UNKNOWN).getName();
    }

    @Mapping(source = "id", target = "cardId")
    @Mapping(source = "userNo", target = "userId")
    @Mapping(source = "vipCardName", target = "cardName")
    @Mapping(source = "payPrice", target = "payAmount", qualifiedByName = "getPrice")
    @Mapping(source = "originalPrice", target = "originalPrice", qualifiedByName = "getPrice")
    @Mapping(source = "contractPrice", target = "contractPrice", qualifiedByName = "getPrice")
    @Mapping(source = "salePrice", target = "salePrice", qualifiedByName = "getPrice")
    @Mapping(source = "reduceAmount", target = "reduceAmount", qualifiedByName = "getPrice")
    @Mapping(source = "effectiveStartTime", target = "beginTime")
    @Mapping(source = "effectiveEndTime", target = "endTime")
    @Mapping(source = "contractNo", target = "contractBizNo")
    @Mapping(source = "lendOrderNo", target = "lendNo")
    @Mapping(source = "orderNo", target = "orderNumber")
    @Mapping(target = "orderStatus", ignore = true)
    @Mapping(source = "payType", target = "payType", qualifiedByName = "payType")
    @Mapping(source = "orderStatus", target = "vipStatus", qualifiedByName = "vipCardStatus")
    @Mapping(source = "contractType", target = "vipTerm", qualifiedByName = "vipTerm")
    @Mapping(source = "buyType", target = "orderBuyType", qualifiedByName = "orderBuyType")
    MemberCardDto vipOrderDtoToMemberCardDto(VipOrderDetailAdminDTO vipOrderDetailDTO);


    @AfterMapping
    default void canReduce(VipOrderDetailAdminDTO vipOrderDetailAdminDTO, @MappingTarget MemberCardDto memberCardDto) {
        memberCardDto.setHasReducePlan(false);
        if (!CollectionUtils.isEmpty(vipOrderDetailAdminDTO.getOrderReduceApplyList()) && vipOrderDetailAdminDTO.getOrderReduceApplyList().get(0) != null) {
            if (ReduceApplyStatusEnum.ENABLE.getCode().equals(vipOrderDetailAdminDTO.getOrderReduceApplyList().get(0).getApplyStatus())) {
                memberCardDto.setHasReducePlan(true);
            }
        }

        memberCardDto.setCanReduce(false);
        if (VipTypeEnum.FEI_YUE.getDesc().equals(vipOrderDetailAdminDTO.getVipCardName())
                && DeductTypeEnum.WITHHOLD.getCode().equals(vipOrderDetailAdminDTO.getDeductType())
                && vipOrderDetailAdminDTO.getCanReduce()
                && !memberCardDto.getHasReducePlan()
                && (VipOrderStatusEnum.PAY_START.getCode().equals(vipOrderDetailAdminDTO.getOrderStatus()) || VipOrderStatusEnum.PAYING.getCode().equals(vipOrderDetailAdminDTO.getOrderStatus()))) {
            memberCardDto.setCanReduce(true);
        }

        if (memberCardDto.getHasReducePlan()) {
            memberCardDto.setPlanId(vipOrderDetailAdminDTO.getOrderReduceApplyList().get(0).getId());
        }
    }

    @Named("orderBuyType")
    static Integer orderBuyType(String orderType) {
        if (orderType == null) {
            return null;
        }

        if (VipBuyTypeEnum.FIRST_SIGN.getCode().equals(orderType)) {
            return 1;
        } else if (VipBuyTypeEnum.MANUAL_RENEW.getCode().equals(orderType)) {
            return 2;
        } else if (VipBuyTypeEnum.AUTO_RENEW.getCode().equals(orderType)) {
            return 3;
        }

        return null;
    }

    @Named("vipTerm")
    static Integer vipTerm(String contractType) {
        if (contractType == null) {
            return null;
        }

        if (ContractTypeEnum.SINGLE_MONTH.getCode().equals(contractType)) {
            return 1;
        } else if (ContractTypeEnum.CONTINUE_MONTH.getCode().equals(contractType)) {
            return 2;
        } else if (ContractTypeEnum.SINGLE_SEASON.getCode().equals(contractType)) {
            return 3;
        } else if (ContractTypeEnum.CONTINUE_SEASON.getCode().equals(contractType)) {
            return 4;
        }

        return null;
    }

    @Named("payType")
    static Integer payType(String payType) {
        if (payType == null) {
            return null;
        }

        if (PayTypeEnum.WECHAT.getCode().equals(payType)) {
            return 1;
        } else if (PayTypeEnum.ALIPAY.getCode().equals(payType)) {
            return 2;
        } else if (PayTypeEnum.DEBIT_CARD.getCode().equals(payType)) {
            return 3;
        }

        return null;
    }

    @Named("vipCardStatus")
    static String vipCardStatus(String status) {
        if (status == null) {
            return null;
        }

        return VipCardStatusEnum.fromVipCardStatus(status).orElse(VipCardStatusEnum.UNKNOWN).getName();
    }

    @Named("getPrice")
    static BigDecimal getPrice(Integer price) {
        if (price == null) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(price).multiply(new BigDecimal("0.01"));
    }

    VipRefundApplyRequest refundRequestToRefundApply(VipCardRefundApply refundApply);

    RightCardRefundDto refundApplyListToRefundDto(RefundApplyListDto refundApplyListDto);

//    RightCardRefundDto vipRefundApplyListToRefundDto(RefundApplyListDTO refundApplyListDTO);

    RightCardRefundRecordDto refundRecordListToRefundDto(RefundListDto refundListDto);

    MemberCardDto oldMemberCardToMemberCardDto(OldMemberCard oldMemberCard);

    MemberCardDto memberCardToMemberCardDto(MemberCard memberCard);

    @Mapping(source = "list", target = "list")
    RightCardRefundRecordDto refundRecordVipCoreListToRefundDto(RefundListAdminDTO refundListDto);

    @Mapping(source = "refundAmount", target = "amount")
    @Mapping(source = "vipType", target = "vipCardType", qualifiedByName = "vipCardTypeConvert")
    @Mapping(source = "refundFlowNo", target = "refundFlowNumber")
    @Mapping(source = "refundStatus", target = "status", qualifiedByName = "vipCardRefundStatusConvert")
    @Mapping(source = "operator", target = "operator")
    @Mapping(source = "vipCardName", target = "cardName")
    @Mapping(source = "refundReason", target = "reason")
    @Mapping(source = "failReason", target = "refundFailReason")
    @Mapping(source = "applyTime", target = "refundApplyCreateTime")
    @Mapping(source = "vipOrderNo", target = "orderNumber")
    @Mapping(source = "refundAccount", target = "refundAccount")
    @Mapping(source = "refundAccountType", target = "refundAccountType", qualifiedByName = "vipCardRefundAccountTypeConvert")
    @Mapping(source = "refundBankName", target = "refundBankName")
    @Mapping(source = "refundChannel", target = "refundApplyChannel", qualifiedByName = "vipCardRefundApplyChannelConvert")
    @Mapping(source = "updateTime", target = "updateTime")
    @Mapping(source = "vipOrderId", target = "vipCardId")
    CardRefund cardRefundListToRefundDetailDTO(RefundDetailAdminDTO refundListDto);

    @Named("vipCardTypeConvert")
    default Integer vipCardTypeConvert(String vipType) {

        if (vipType == null) {
            return null;
        }

        if (VipTypeEnum.FEI_XIANG.getCode().equals(vipType)) {
            return 3;
        } else if (VipTypeEnum.FEI_YUE.getCode().equals(vipType)) {
            return 4;
        }
        return null;
    }

    @Named("vipCardRefundStatusConvert")
    default Integer vipCardRefundStatusConvert(String refundStatus) {
        if (refundStatus == null) {
            return null;
        }

        if (RefundStatusEnum.REFUNDING.getCode().equals(refundStatus)) {
            return 0;
        } else if (RefundStatusEnum.REFUND_FAIL.getCode().equals(refundStatus)) {
            return 2;
        } else if (RefundStatusEnum.REFUND_START.getCode().equals(refundStatus)) {
            return 0;
        } else if (RefundStatusEnum.REFUND_SUCCESS.getCode().equals(refundStatus)) {
            return 1;
        }
        return null;
    }

    @Named("vipCardRefundAccountTypeConvert")
    default Integer vipCardRefundAccountTypeConvert(String refundAccountType) {
        if (refundAccountType == null) {
            return null;
        }
        if (PayTypeEnum.DEBIT_CARD.getCode().equals(refundAccountType)) {
            return 1;
        } else if (PayTypeEnum.ALIPAY.getCode().equals(refundAccountType)) {
            return 2;
        } else if (PayTypeEnum.WECHAT.getCode().equals(refundAccountType)) {
            return 4;
        }
        return null;
    }

    @Named("vipCardRefundApplyChannelConvert")
    default Integer vipCardRefundApplyChannelConvert(String refundChannel) {
        if (refundChannel == null) {
            return null;
        }
        if (RefundChannelEnum.ORIGINAL_REFUND.getCode().equals(refundChannel)) {
            return 1;
        } else if (RefundChannelEnum.OFFLINE_REFUND.getCode().equals(refundChannel)) {
            return 2;
        }
        return null;
    }

    @Mapping(source = "rightsName", target = "name")
    @Mapping(source = "receiveAmount", target = "num")
    @Mapping(source = "rightsTypeName", target = "itemTypeName")
    @Mapping(source = "rightsItemId", target = "itemId")
    @Mapping(source = "receiveTime", target = "createTime")
    @Mapping(source = "totalPrice", target = "price")
    @Mapping(source = "receiveMethod", target = "type", qualifiedByName = "mapReceiveMethod")
    @Mapping(source = "receiveDetailList", target = "receiveDetailList")
    ReceiveLogAdminDto receiveLogAdminDtoToRightsLogDTO(RightsReceiveLogAdminDTO refundListDto);

    @Mapping(source = "rightsPrice", target = "itemPrice")
    @Mapping(source = "whetherUsed", target = "itemHasUsed")
    @Mapping(source = "rightCost", target = "itemCost")
    @Mapping(source = "receiveMessage", target = "itemMessage")
    @Mapping(source = "receiveTime", target = "receiveTime")
    ReceiveDetailDto receiveDetailDtoToRightsReceiveDetailDTO(RightsReceiveDetailAdminDTO rightsReceiveDetailDTO);

    @Named("mapReceiveMethod")
    default Integer mapReceiveMethod(String receiveMethod) {
        if (receiveMethod == null) {
            return null;
        }
        switch (receiveMethod) {
            case "receive"://直接领取
                return 1;
            case "gold_exchange"://金币兑换
                return 2;
        }
        return null;
    }

    @Named("mapStatusToNumber")
    default Integer mapStatusToNumber(String status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case "audit_start":
            case "auditing":
            case "audit_pass":
            case "process_start":
            case "processing":
            case "process_success":
                return 0;
            case "process_fail":
                return 2;
            case "audit_reject":
            case "audit_cancel":
                return 3;
            default:
                return null;
        }
    }

    @Named("mapStatusTo")
    default Integer mapStatusTo(Integer status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case 1:
            case 2:
            case 3:
            case 4:
                return 0;
            case 5:
                return 2;
            case 6:
                return 3;
            default:
                return null;
        }
    }

    @Named("isRefundCancel")
    default Boolean isRefundCancel(String status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case "audit_start":
            case "auditing":
                return true;
            default:
                return false;
        }
    }

    @Mapping(source = "contractPrice", target = "contractPrice", qualifiedByName = "getPrice")
    @Mapping(source = "reduceAmount", target = "reduceAmount", qualifiedByName = "getPrice")
    @Mapping(source = "salePrice", target = "salePrice", qualifiedByName = "getPrice")
    @Mapping(source = "applyStatus", target = "planStatus", qualifiedByName = "planStatus")
    @Mapping(source = "vipBuyType", target = "orderBuyType", qualifiedByName = "orderBuyType")
    @Mapping(source = "vipContractType", target = "vipTerm", qualifiedByName = "vipTerm")
    VipOrderReduceDetailAdmin vipOrderReduceDetailDtoToVipOrderReduceDetailAdmin(VipOrderReduceDetailAdminDTO vipOrderReduceDetailDTO);

    @Mapping(source = "reduceAmount", target = "reduceAmount", qualifiedByName = "getPrice")
    @Mapping(source = "applyStatus", target = "planStatus", qualifiedByName = "planStatus")
    @Mapping(source = "createOperator", target = "creator")
    @Mapping(source = "updateOperator", target = "updater")
    VipOrderReducePriceApplyAdmin vipOrderReducePriceApplyDtoToVipOrderReducePriceApplyAdmin(VipOrderReducePriceApplyAdminDTO vipOrderReducePriceApplyDTO);

    @Named("planStatus")
    default Integer planStatus(String status) {
        if (StringUtils.isEmpty(status)) {
            return null;
        }
        if (ReduceApplyStatusEnum.ENABLE.getCode().equals(status)) {
            return 1;
        }
        if (ReduceApplyStatusEnum.USED.getCode().equals(status)) {
            return 3;
        }
        if (ReduceApplyStatusEnum.DISABLED.getCode().equals(status)) {
            return 2;
        }
        return null;
    }
}
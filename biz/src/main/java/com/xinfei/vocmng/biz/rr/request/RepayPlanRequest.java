/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import com.xinfei.vocmng.biz.rr.dto.BankDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RepayPlanRequest, v 0.1 2024-03-28 16:47 junjie.yan Exp $
 */
@Data
public class RepayPlanRequest {
    @ApiModelProperty("方案编号(修改)")
    private Long planId;

    @ApiModelProperty("custNo")
    @NotBlank(message = "custNo不能为空")
    private String custNo;

    @ApiModelProperty("userNo")
    @NotBlank(message = "userNo不能为空")
    private String userNo;

    @ApiModelProperty("方案类型：1：还当期，2：提前结清")
    @NotNull(message = "planType不能为空")
    private Integer planType;

    @ApiModelProperty("还款方式（1：app自助，2：系统代扣，3：聚合支付，4：线下还款）")
    @NotNull(message = "repayMethod不能为空")
    private Integer repayMethod;

    @ApiModelProperty("手机号（聚合支付、线下还款发送短信使用）")
    private String mobile;

    @ApiModelProperty("是否发送短信（线下还款发送短信使用）")
    private Boolean isSendSms;

    @ApiModelProperty("是否包含按日计息订单")
    private Boolean hasDailyType;

    @ApiModelProperty(value = "银行卡列表")
    private List<BankDto> banks;

    @ApiModelProperty("是否发送（1：发送，2：保存）")
    @NotNull(message = "isSend不能为空")
    private Integer isSend;

    @ApiModelProperty("方案失效时间")
    @NotNull(message = "endTime不能为空")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "银行卡绑卡id，系统代扣必传")
    private String bankCardId;

    @ApiModelProperty("参与抵扣借据号")
    private List<String> beReducedLoanNos;

    @ApiModelProperty("还款方案明细")
    @NotEmpty(message = "repayPlanDetails不能为空")
    private List<RepayPlanDetailRequest> repayPlanDetails;

    @ApiModelProperty("是否审批")
    private boolean needReview;

    @ApiModelProperty("是否审批通过")
    private Boolean isApproved;

    @ApiModelProperty("审批理由")
    private String reason;

    @ApiModelProperty("投诉渠道")
    private String complaintChannelLv1;

    @ApiModelProperty("投诉渠道")
    private String complaintChannelLv2;

}
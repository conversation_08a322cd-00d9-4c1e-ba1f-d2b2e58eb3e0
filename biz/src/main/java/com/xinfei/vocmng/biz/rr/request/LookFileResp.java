/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ LookFileResp, v 0.1 2024/6/7 11:44 wancheng.qu Exp $
 */
@Data
public class LookFileResp implements Serializable {

    @ApiModelProperty(value = "文件地址")
    private List<String> url;

    @ApiModelProperty(value = "不能查看原因")
    private String msg;

}
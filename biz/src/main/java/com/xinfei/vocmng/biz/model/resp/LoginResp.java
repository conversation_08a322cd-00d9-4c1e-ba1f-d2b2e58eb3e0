package com.xinfei.vocmng.biz.model.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoginResp {
    @ApiModelProperty(value = "用户登录后的token")
    private String token;

    @ApiModelProperty(value = "用户资源权限")
    private List<String> resource;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "userIdentify")
    private String userIdentify;

    @ApiModelProperty(value = "departmentType")
    private Integer departmentType;

    @ApiModelProperty(value = "员工编号")
    private Long id;

    @ApiModelProperty("是否实时质检辅助(0:否,1:是)")
    private Integer realTimeAssistance;

    @ApiModelProperty("sso用户id")
    private String ssoUserId;

    @ApiModelProperty("用户手机号")
    private String mobile;

    public LoginResp(String token, String userName) {
        this.token = token;
        this.userName = userName;
    }

    public LoginResp(String token, String userName, String userIdentify, Integer departmentType, Long id, Integer realTimeAssistance, Long ssoUserId, String mobile) {
        this.token = token;
        this.userName = userName;
        this.userIdentify = userIdentify;
        this.departmentType = departmentType;
        this.id = id;
        this.realTimeAssistance = realTimeAssistance;
        this.ssoUserId = String.valueOf(ssoUserId);
        this.mobile = mobile;
    }
}

/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.model.resp.AmountAdjustmentRecordResp;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ AccountAmountApi, v 0.1 2024/8/12 12:04 you.zhang Exp $
 */
@Api(tags = "客户账户额度接口")
@RequestMapping("/account/amount")
public interface AccountAmountApi {

    @ApiOperation("查询调额记录接口")
    @GetMapping("/adjustment/records")
    public ApiResponse<List<AmountAdjustmentRecordResp>> queryAdjustmentRecords(@ApiParam(value = "app") @RequestParam(value = "app",required = false) String app,
                                                                                @ApiParam(value = "客户号") @RequestParam(value = "custNo",required = false) String custNo,
                                                                                @ApiParam(value = "调额类型") @RequestParam(value = "adjustType",required = false) String adjustType);
}

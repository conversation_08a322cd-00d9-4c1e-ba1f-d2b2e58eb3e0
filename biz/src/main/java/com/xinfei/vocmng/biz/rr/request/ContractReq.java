/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ ContractReq, v 0.1 2024/8/23 15:27 wancheng.qu Exp $
 */
@Data
public class ContractReq implements Serializable {

    @ApiModelProperty("bind_card/绑卡，activation/激活，cash/取现，credit_investigation/征信,consume/消费贷（协议才有该类型）")
    @NotBlank(message = "type不能为空")
    private String type;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("订单号")
    private String orderNO;

}
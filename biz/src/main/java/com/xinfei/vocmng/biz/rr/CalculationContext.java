/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr;

/**
 * <AUTHOR>
 * @version $ CalculationContext, v 0.1 2025-04-29 11:23 junjie.yan Exp $
 */

import com.xinfei.vocmng.biz.rr.dto.bill.PlanDetailDto;
import com.xinfei.vocmng.biz.rr.response.CalculateFee;
import com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig;
import lombok.Data;

import java.math.BigDecimal;

// 定义一个上下文对象来传递必要的参数
@Data
public class CalculationContext {
    private final BigDecimal value;
    private final PlanDetailDto initPlan;
    private final PlanDetailDto deductPlan;
    private final CalculateFee calculateFee;
    private final BigDecimal totalRedDeduct; // 可能只对 Settle_Fee 有用
    private final FeeStrategyConfig config;

    public CalculationContext(BigDecimal value, PlanDetailDto initPlan, PlanDetailDto deductPlan, CalculateFee calculateFee, BigDecimal totalRedDeduct, FeeStrategyConfig config) {
        this.value = value;
        this.initPlan = initPlan;
        this.deductPlan = deductPlan;
        this.calculateFee = calculateFee;
        this.totalRedDeduct = totalRedDeduct;
        this.config = config;
    }
}
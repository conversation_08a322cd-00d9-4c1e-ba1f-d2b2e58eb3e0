package com.xinfei.vocmng.biz.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @version $ UdeskCallResultEnum, v 0.1 2025/3/7 10:34 shaohui.chen Exp $
 */
@AllArgsConstructor
@Getter
public enum UdeskCallResultEnum {
    CUSTOMER_NOT_ACCEPT("客户未接", CallResultEnum.UN_CONNECT),
    CUSTOMER_ACCEPT("客户接听", CallResultEnum.CONNECT),
    AGENT_NOT_ACCEPT("客服未接", CallResultEnum.UN_CONNECT),
    AGENT_REJECT("客服拒接", CallResultEnum.UN_CONNECT),
    AGENT_ACCEPT("客服接听", CallResultEnum.CONNECT),
    NO_QUEUE_SELECTED("未选择队列", CallResultEnum.UN_CONNECT);

    private final String thirdPartyDesc;
    private final CallResultEnum mySideEnum;

    /**
     * 根据三方描述文本，查找对应的我方 code。
     * 若未找到匹配，抛出异常或可自行返回默认值。
     */
    public static int getCallResultCodeByUdeskDesc(String desc) {
        for (UdeskCallResultEnum e : UdeskCallResultEnum.values()) {
            if (e.getThirdPartyDesc().equals(desc)) {
                // 返回对应的我方 code
                return e.getMySideEnum().getCode();
            }
        }
        // 未匹配到时，可自行处理（抛异常 / 返回默认值等）
        return -1;
    }
}

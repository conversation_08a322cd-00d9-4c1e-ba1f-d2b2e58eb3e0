package com.xinfei.vocmng.biz.util;

import com.alibaba.fastjson2.JSON;
import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.itl.client.feign.impl.PayFeignClientImpl;
import com.xinfei.vocmng.itl.model.enums.TxnTypeEnum;
import com.xinfei.vocmng.itl.rr.FinServiceType;
import com.xinfei.vocmng.itl.rr.VoucherGenerateRequest;
import com.xinfei.vocmng.itl.rr.VoucherGenerateResponse;
import com.xinfei.vocmng.itl.rr.dto.BankDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class IDCardUtilTest {

    @Autowired
    private RedisServiceUtils redisServiceUtils;

    @Autowired
    private PayFeignClientImpl payFeignClient;

    @Test
    public void getAge() {
        Integer age = IDCardUtil.getAge("");
        System.out.println(age);
    }

    @Test
    public void getBirthday() {
        String birthday = IDCardUtil.getBirthday("");
        System.out.println(birthday);
    }

    @Test
    public void getBankList() {
        List<BankDto> bankDtos = redisServiceUtils.getBankList();
        System.out.println(bankDtos);
    }

    @Test
    public void getFinBaseInfo() {
        FinServiceType finServiceType = redisServiceUtils.getFinBaseInfo();
        System.out.println(finServiceType);
    }

    @Test
    public void getVoucherGenerateResponse() {
        VoucherGenerateRequest request = new VoucherGenerateRequest();
        request.setBizOrderNo("2025071500137350890000200500");
        request.setTxnTypeEnum(TxnTypeEnum.PAYMENT);
        VoucherGenerateResponse voucherGenerateResponse = payFeignClient.generateVoucher(request);
        System.out.println(JSON.toJSONString(voucherGenerateResponse));

    }


}
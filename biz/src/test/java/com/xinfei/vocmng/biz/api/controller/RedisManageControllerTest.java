package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.biz.constants.RedisKeyConstants;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis管理Controller测试类
 *
 * <AUTHOR>
 * @version $ RedisManageControllerTest, v 0.1 2025/5/1 $
 */
@Slf4j
public class RedisManageControllerTest extends TechplayDevTestBase {

    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final String TEST_SET_KEY = "test:redis:manage:set";
    private static final String TEST_STREAM_KEY = "test:redis:manage:stream";

    @BeforeEach
    public void setUp() {
        // 清理测试数据
        redisTemplate.delete(TEST_SET_KEY);
        redisTemplate.delete(TEST_STREAM_KEY);

        // 准备测试数据 - Set
        redisTemplate.opsForSet().add(TEST_SET_KEY, "value1", "value2", "value3");

        // 准备测试数据 - Stream
        Map<String, String> streamData1 = new HashMap<>();
        streamData1.put("field1", "value1");
        streamData1.put("field2", "value2");
        redisTemplate.opsForStream().add(TEST_STREAM_KEY, streamData1);

        Map<String, String> streamData2 = new HashMap<>();
        streamData2.put("field1", "value3");
        streamData2.put("field2", "value4");
        redisTemplate.opsForStream().add(TEST_STREAM_KEY, streamData2);
    }

    @Test
    public void testGetSetMembers() throws Exception {
        // 调用API获取Set成员
        ApiResponse<Set<String>> response = get("api/redis/set/" + TEST_SET_KEY, ApiResponse.class);

        log.info("Get Set members response: {}", response);

        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertEquals(3, response.getData().size());
        assertTrue(response.getData().contains("value1"));
        assertTrue(response.getData().contains("value2"));
        assertTrue(response.getData().contains("value3"));
    }

    @Test
    public void testDeleteSetMember() throws Exception {
        // 调用API删除Set成员
        ApiResponse<Boolean> response = get("api/redis/set/" + TEST_SET_KEY + "/member?value=value1", ApiResponse.class);

        log.info("Delete Set member response: {}", response);

        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertTrue(response.getData());

        // 验证成员已被删除
        Set<String> members = redisTemplate.opsForSet().members(TEST_SET_KEY);
        assertNotNull(members);
        assertEquals(2, members.size());
        assertFalse(members.contains("value1"));
        assertTrue(members.contains("value2"));
        assertTrue(members.contains("value3"));
    }

    @Test
    public void testDeleteSet() throws Exception {
        // 调用API删除Set
        ApiResponse<Boolean> response = get("api/redis/set/" + TEST_SET_KEY, ApiResponse.class);

        log.info("Delete Set response: {}", response);

        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertTrue(response.getData());

        // 验证Set已被删除
        Boolean exists = redisTemplate.hasKey(TEST_SET_KEY);
        assertFalse(exists);
    }

    @Test
    public void testGetStreamMessages() throws Exception {
        // 调用API获取Stream消息
        ApiResponse<List<Map<String, Object>>> response = get("api/redis/stream/" + TEST_STREAM_KEY, ApiResponse.class);

        log.info("Get Stream messages response: {}", response);

        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertEquals(2, response.getData().size());

        // 验证消息内容
        Map<String, Object> message1 = response.getData().get(0);
        assertNotNull(message1.get("id"));
        assertNotNull(message1.get("data"));

        Map<String, Object> data1 = (Map<String, Object>) message1.get("data");
        assertEquals("value1", data1.get("field1"));
        assertEquals("value2", data1.get("field2"));
    }

    @Test
    public void testDeleteStreamMessage() throws Exception {
        // 获取Stream消息ID
        List<Map<String, Object>> messages = new ArrayList<>();
        redisTemplate.opsForStream().read(
                org.springframework.data.redis.connection.stream.StreamReadOptions.empty().count(1),
                org.springframework.data.redis.connection.stream.StreamOffset.fromStart(TEST_STREAM_KEY))
                .forEach(record -> {
                    Map<String, Object> message = new HashMap<>();
                    message.put("id", record.getId().getValue());
                    message.put("data", record.getValue());
                    messages.add(message);
                });

        String messageId = (String) messages.get(0).get("id");

        // 调用API删除Stream消息
        ApiResponse<Boolean> response = get("api/redis/stream/" + TEST_STREAM_KEY + "/message?messageId=" + messageId, ApiResponse.class);

        log.info("Delete Stream message response: {}", response);

        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertTrue(response.getData());

        // 验证消息已被删除
        long count = redisTemplate.opsForStream().size(TEST_STREAM_KEY);
        assertEquals(1, count);
    }

    @Test
    public void testDeleteStream() throws Exception {
        // 调用API删除Stream
        ApiResponse<Boolean> response = get("api/redis/stream/" + TEST_STREAM_KEY, ApiResponse.class);

        log.info("Delete Stream response: {}", response);

        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertTrue(response.getData());

        // 验证Stream已被删除
        Boolean exists = redisTemplate.hasKey(TEST_STREAM_KEY);
        assertFalse(exists);
    }

    @Test
    public void testGetKeys() throws Exception {
        // 创建一些测试键
        String prefix = "test:redis:manage:keys:" + UUID.randomUUID().toString().substring(0, 8);
        redisTemplate.opsForValue().set(prefix + ":key1", "value1");
        redisTemplate.opsForValue().set(prefix + ":key2", "value2");
        redisTemplate.opsForValue().set(prefix + ":key3", "value3");

        // 调用API获取匹配的键
        ApiResponse<Set<String>> response = get("api/redis/keys?pattern=" + prefix + ":*", ApiResponse.class);

        log.info("Get keys response: {}", response);

        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertEquals(3, response.getData().size());
        assertTrue(response.getData().contains(prefix + ":key1"));
        assertTrue(response.getData().contains(prefix + ":key2"));
        assertTrue(response.getData().contains(prefix + ":key3"));

        // 清理测试数据
        redisTemplate.delete(prefix + ":key1");
        redisTemplate.delete(prefix + ":key2");
        redisTemplate.delete(prefix + ":key3");
    }
}

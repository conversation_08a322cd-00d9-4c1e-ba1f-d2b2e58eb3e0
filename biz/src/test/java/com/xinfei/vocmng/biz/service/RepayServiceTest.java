/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.google.common.collect.Sets;
import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentPlanResultResponse;
import com.xinfei.repaytrade.facade.rr.response.RepaymentPlanInvalidResponse;
import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.rr.dto.BankFlow;
import com.xinfei.vocmng.biz.rr.request.BankFlowRequest;
import com.xinfei.vocmng.dal.mapper.RepaymentPlanDetailMapper;
import com.xinfei.vocmng.dal.po.RepaymentPlanDetail;
import com.xinfei.vocmng.itl.client.feign.RepayFacadeClient;
import com.xinfei.xfframework.common.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version $ RepayService, v 0.1 2024/4/1 11:21 wancheng.qu Exp $
 */

@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class RepayServiceTest {

    @Autowired
    private RepayService repayService;
    @Resource
    private RepaymentPlanDetailMapper repaymentPlanDetailMapper;
    @Resource
    private RepaymentTaskService repaymentTaskService;

    @Resource
    private RepayFacadeClient repayFacadeClient;


    @Test
    public void test() {
        RepaymentPlanInvalidResponse s = repayFacadeClient.repaymentPlanInvalid(Collections.singletonList("1"), null, "过期失效");
        System.out.println("res====" + JsonUtil.toJson(s));
    }

    @Test
    public void updateRepaymentPlanStatus() {
        Set<Long> s = Sets.newHashSet(14l, 15l);
        repayService.updateRepaymentPlanStatus(new ArrayList<>(s), 0);
    }

    @Test
    public void updateDetail() {
        QueryRepaymentPlanResultResponse.PlanDetailResult planDetailResult = new QueryRepaymentPlanResultResponse.PlanDetailResult();
        planDetailResult.setPlanDetailId("8");
        planDetailResult.setStatus("0");
        planDetailResult.setUseStatus("1");
        repayService.updateDetail(planDetailResult);
    }

    @Test
    public void findPlanStatusUpdateInfo() {
        List<Long> updateInfos = repaymentPlanDetailMapper.findPlanStatusUpdateInfo();
        List<RepaymentPlanDetail> res = repaymentPlanDetailMapper.getRepaymentInvalidDetailList();
        System.out.println("update==========" + JsonUtil.toJson(updateInfos) + "--------detail======" + JsonUtil.toJson(res));
    }

    @Test
    public void updateRepaymentStatus() {
        repaymentTaskService.updateRepaymentStatus();
    }

    @Test
    public void bankFlow() {
        BankFlowRequest request = new BankFlowRequest();
        request.setEndDate("2024-05-20 00:00:00");
        request.setStartDate("2024-05-15 00:00:00");
        request.setPageSize(10);
        request.setCurrentPage(1);
        PageResultResponse<BankFlow> response = repayService.bankFlow(request);
        System.out.println(response);
    }


}
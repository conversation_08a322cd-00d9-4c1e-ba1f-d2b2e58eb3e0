package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.rr.dto.CustomerDetailDto;
import com.xinfei.vocmng.biz.rr.dto.UserNoAppDto;
import com.xinfei.vocmng.biz.rr.request.GetCustomerListRequest;
import com.xinfei.vocmng.biz.rr.request.GetCustomerRequest;
import com.xinfei.vocmng.biz.rr.request.GetUserNoListRequest;
import com.xinfei.vocmng.biz.rr.request.SendMessageRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class CustomerServiceImplTest {

    @Resource
    private CustomerServiceImpl customerService;

    @Test
    public void getCustomerList() {
        GetCustomerListRequest request = new GetCustomerListRequest();
        request.setPageSize(1);
        request.setCurrentPage(1);
        request.setMobile("13267036666");
        customerService.getCustomerList(request);
    }

    @Test
    public void getUserNoList() {
        GetUserNoListRequest request = new GetUserNoListRequest();
        request.setIdCard("131102199312091036");
        request.setRegisterApp("xyf01");
        request.setMobile("13267036666");
        List<UserNoAppDto> userNoAppDtos =  customerService.getUserNoList(request);
        System.out.println(userNoAppDtos);
    }

    @Test
    public void getAmt() {
        CustomerDetailDto dto = new CustomerDetailDto();

        customerService.getAmt(dto, "CTL031f7d199ff409e05e4610d8fa6006cb4", "xyf01");
    }

    @Test
    public void getCustomerDetailByCustNo() {
        CustomerDetailDto dto = new CustomerDetailDto();

        customerService.getCustomerDetailByCustNo(dto, null, "CTL0314a73126897406316dce41b47036b73", "xyf01");
    }

    @Test
    public void sendMessage() {
        SendMessageRequest request = new SendMessageRequest();
        request.setUserNo("1639203089096191247");
        request.setApp("xyf01");
        request.setTemplateId("hehftp042710");
        Boolean result = customerService.sendMessage(request);
        System.out.println(result);
    }

    @Test
    public void getCustomerDetail() {
        GetCustomerRequest request = new GetCustomerRequest();
        request.setUserNo("111111115822020");
        CustomerDetailDto result = customerService.getCustomerDetail(request);
        System.out.println(result);
    }
}
package com.xinfei.vocmng.biz.service;

import com.xinfei.huttalegal.facade.rr.res.LegalAgencyDetail;
import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.rr.dto.DiversionOrderDto;
import com.xinfei.vocmng.biz.rr.dto.IVRLatestBillDto;
import com.xinfei.vocmng.biz.rr.dto.IVRLoanSettlementDto;
import com.xinfei.vocmng.biz.rr.dto.IVRMonthlyAmountDto;
import com.xinfei.vocmng.biz.rr.dto.OrderDto;
import com.xinfei.vocmng.biz.rr.dto.RepaymentsDto;
import com.xinfei.vocmng.biz.rr.dto.bill.LoanPlanDto;
import com.xinfei.vocmng.biz.rr.request.AgencyDetailRequest;
import com.xinfei.vocmng.biz.rr.request.ApiOrderRequest;
import com.xinfei.vocmng.biz.rr.request.CanRepayRequest;
import com.xinfei.vocmng.biz.rr.request.GetBillListRequest;
import com.xinfei.vocmng.biz.rr.request.GetOrderDetailRequest;
import com.xinfei.vocmng.biz.rr.request.GetOrderListRequest;
import com.xinfei.vocmng.biz.rr.request.GetRepaymentsRequest;
import com.xinfei.vocmng.biz.rr.response.CanRepayResponse;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.itl.rr.ProductDetailInfo;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * LoanService服务的单元测试类
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class LoanServiceJUnitTest {

    @Resource
    private LoanService loanService;

    @Test
    @DisplayName("测试查询订单列表")
    public void testQueryOrderList() {
        // 准备测试数据
        GetOrderListRequest request = new GetOrderListRequest();
        request.setPageSize(10);
        request.setCurrentPage(1);
        // 设置查询条件
        request.setMobile("13800138000"); // 使用测试手机号

        // 执行测试
        Paging<OrderDto> response = loanService.queryOrderList(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试查询订单详情")
    public void testQueryOrderDetail() {
        // 准备测试数据
        GetOrderDetailRequest request = new GetOrderDetailRequest();
        request.setOrderNo("2025042700126800000078887797"); // 使用测试订单号
        request.setOrderType("MAIN"); // 设置订单类型

        // 执行测试
        OrderDto response = loanService.queryOrderDetail(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试订单取消")
    public void testOrderCancel() {
        // 准备测试数据
        String orderNo = "2025012100126900000078962500"; // 使用测试订单号

        // 执行测试
        Boolean response = loanService.orderCancel(orderNo);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试根据资方订单号查询订单号")
    public void testQueryByOutOrderNumber() {
        // 准备测试数据
        GetOrderListRequest request = new GetOrderListRequest();
        request.setOutOrderNumber("testOutOrderNumber"); // 使用测试资方订单号

        // 执行测试
        String response = loanService.queryByOutOrderNumber(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试根据渠道方订单号查询订单号")
    public void testQueryByChannelOrderNumber() {
        // 准备测试数据
        GetOrderListRequest request = new GetOrderListRequest();
        request.setChannelOrderNumber("testChannelOrderNumber"); // 使用测试渠道方订单号

        // 执行测试
        String response = loanService.queryByChannelOrderNumber(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试查询账单列表")
    public void testQueryBillList() {
        // 准备测试数据
        GetBillListRequest request = new GetBillListRequest();
        List<String> loanNos = new ArrayList<>();
        loanNos.add("testLoanNo"); // 使用测试借据号
        request.setLoanNos(loanNos);

        Map<String, String> loanNoOrderTypes = new HashMap<>();
        loanNoOrderTypes.put("testLoanNo", "MAIN");
        request.setLoanNoOrderTypes(loanNoOrderTypes);

        // 执行测试
        List<LoanPlanDto> response = loanService.queryBillList(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试查询是否可以还款")
    public void testQueryCanRepay() {
        // 准备测试数据
        CanRepayRequest request = new CanRepayRequest();
        request.setLoanNo("testLoanNo"); // 使用测试借据号
        request.setTerm("1"); // 设置期数
        request.setLoanStatus("RP"); // 设置借据状态

        // 执行测试
        CanRepayResponse response = loanService.queryCanRepay(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试查询还款记录")
    public void testQueryRepayments() {
        // 准备测试数据
        GetRepaymentsRequest request = new GetRepaymentsRequest();
        List<String> loanNos = new ArrayList<>();
        loanNos.add("testLoanNo"); // 使用测试借据号
        request.setLoanNo(loanNos);
        request.setPageSize(10);
        request.setCurrentPage(1);

        // 执行测试
        Paging<RepaymentsDto> response = loanService.queryRepayments(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试查询API导流订单列表")
    public void testQueryApiOrderList() {
        // 准备测试数据
        ApiOrderRequest request = new ApiOrderRequest();
        request.setPageSize("10");
        request.setCurrentPage("1");
        request.setMobile("18054083958"); // 使用测试手机号

        // 执行测试
        Paging<DiversionOrderDto> response = loanService.queryApiOrderList(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试查询产品列表")
    public void testQueryProductList() {
        // 执行测试
        List<ProductDetailInfo> response = loanService.queryProductList();

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试查询法诉机构详情")
    public void testQueryAgencyDetail() {
        // 准备测试数据
        AgencyDetailRequest request = new AgencyDetailRequest();
        request.setLoanNo("testLoanNo"); // 使用测试借据号

        // 执行测试
        LegalAgencyDetail response = loanService.queryAgencyDetail(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试查询IVR近期账单信息")
    public void testQueryIVRLatestBill() {
        // 准备测试数据
        String customNo = "CTL0c3f1a64ec7c1ed3edc84aed1f97ef74f"; // 使用测试客户号
        String mobile = "13800138000"; // 使用测试手机号

        // 执行测试
        IVRLatestBillDto response = loanService.queryIVRLatestBill(customNo, mobile);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
        System.out.println("IVR近期账单查询结果: " + response);
    }

    @Test
    @DisplayName("测试查询IVR当月应还金额信息")
    public void testQueryIVRMonthlyAmount() {
        // 准备测试数据
        String customNo = "CTL0c3f1a64ec7c1ed3edc84aed1f97ef74f"; // 使用测试客户号
        String mobile = "13800138000"; // 使用测试手机号

        // 执行测试
        IVRMonthlyAmountDto response = loanService.queryIVRMonthlyAmount(customNo, mobile);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
        System.out.println("IVR当月应还金额查询结果: " + response);
    }

    @Test
    @DisplayName("测试查询IVR在贷未结清订单信息")
    public void testQueryIVRLoanSettlement() {
        // 准备测试数据
        String customNo = "CTL0c3f1a64ec7c1ed3edc84aed1f97ef74f"; // 使用测试客户号
        String mobile = "13800138000"; // 使用测试手机号

        // 执行测试
        IVRLoanSettlementDto response = loanService.queryIVRLoanSettlement(customNo, mobile);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
        System.out.println("IVR在贷未结清订单查询结果: " + response);
    }
}

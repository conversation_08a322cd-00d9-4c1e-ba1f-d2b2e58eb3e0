package com.xinfei.vocmng.biz.feign;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.MemberInterestFeignClient;
import com.xinfei.vocmng.itl.rr.MemberCard;
import com.xinfei.vocmng.itl.rr.MemberCardRequest;
import com.xinfei.vocmng.itl.rr.MemberCardUseInfo;
import com.xinfei.vocmng.itl.rr.MemberCardUsedRequest;
import com.xinfei.vocmng.itl.rr.MemberInterestResponse;
import com.xinfei.vocmng.itl.rr.OldMemberCard;
import com.xinfei.vocmng.itl.rr.RightCardPackInfo;
import com.xinfei.vocmng.itl.rr.RightPackRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ MemberInterestFeignTest, v 0.1 2023/12/23 12:38 qu.lu Exp $
 */
@Slf4j
public class MemberInterestFeignTest extends TechplayDevTestBase {

    @Autowired
    private MemberInterestFeignClient memberInterestFeignClient;

    @Test
    public void queryRightCardInfo(){
        RightPackRequest request = new RightPackRequest();
        request.setOrderNumber("20240110162052110676");

        MemberInterestResponse<RightCardPackInfo> response = memberInterestFeignClient.loadRightCardInfo(request);
        log.info("query right card info, response={}",response);
    }

    @Test
    public void getPayRecords(){
        MemberCardRequest request = new MemberCardRequest();
        request.setCardStatus(2);
//        request.setMobile("13262633594");

        MemberInterestResponse<List<OldMemberCard>> response = memberInterestFeignClient.queryPayRecords(request);
        log.info("pay records response={}",response);
    }

    @Test
    public void getNewVipRecords(){
        MemberCardRequest request = new MemberCardRequest();
//        request.setMobile("15050259144");
//        request.setMobile("13262633594");
//        request.setCardStatus(2);
        request.setPage(1);
        request.setPageSize(10);


        MemberInterestResponse<List<MemberCard>> response = memberInterestFeignClient.queryNewVipRecords(request);
        log.info("response={}",response);
    }

    @Test
    public void queryUsedInfo(){
        MemberCardUsedRequest request = new MemberCardUsedRequest();
        request.setCardId(2836);
        request.setType(1);

        MemberInterestResponse<List<MemberCardUseInfo>> response = memberInterestFeignClient.queryMemberCardUsedInfo(request);
        log.info("query used info response={}",response);
    }
}

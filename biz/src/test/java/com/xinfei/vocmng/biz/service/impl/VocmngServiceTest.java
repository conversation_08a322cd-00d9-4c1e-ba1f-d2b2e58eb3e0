/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.req.UserLabelRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import javax.xml.bind.ValidationException;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * VocmngService 测试类
 *
 * <AUTHOR>
 * @version $ VocmngServiceTest, v 0.1 2025/5/15 19:00 shaohui.chen Exp $
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
class VocmngServiceTest {

    @Resource
    private VocmngService vocmngService;


    /**
     * 测试获取用户标签 - 通过用户编号
     */
    @Test
    void getUserLabelByUserNo() throws ValidationException {
        // 创建请求
        UserLabelRequest request = new UserLabelRequest();
        request.setUserNo(412L); // 使用一个已知的用户编号

        // 调用服务方法
        String response = vocmngService.getUserLabel(412L);

        // 验证结果
        assertNotNull(response, "响应不应为null");
        log.info("======{}",response);
    }

    /**
     * 测试获取用户标签 - 空参数
     */
    @Test
    void getUserLabelWithEmptyParams() throws ValidationException {

        // 调用服务方法
        String response = vocmngService.getUserLabel(null);

        // 验证结果
        assertNotNull(response, "响应不应为null");
        log.info("空参数请求响应: {}", response);
    }

}

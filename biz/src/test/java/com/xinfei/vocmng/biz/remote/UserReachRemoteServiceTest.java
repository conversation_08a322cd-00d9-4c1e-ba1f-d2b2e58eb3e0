package com.xinfei.vocmng.biz.remote;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.CallRecordDto;
import com.xinfei.vocmng.biz.rr.dto.SmsRecordsDto;
import com.xinfei.vocmng.biz.rr.request.QueryCallListRequest;
import com.xinfei.vocmng.biz.rr.request.QuerySmsRecordRequest;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ UserReachRemoteServiceTest, v 0.1 2023/12/28 15:17 qu.lu Exp $
 */
@Slf4j
public class UserReachRemoteServiceTest extends TechplayDevTestBase {
    @Autowired
    private UserReachRemoteService userReachRemoteService;
    @Resource
    private CisFacadeClient cisFacadeClient;

    @Test
    public void queryUserNoByMobilMd5(){
        cisFacadeClient.queryUserNoByMobilMd5("tP/9jDlrKi4cb53llfe5Mw==");
    }

    @Test
    public void querySmsList(){
        QuerySmsRecordRequest request = new QuerySmsRecordRequest();
        request.setMobile("17612194271");
        ApiResponse<Paging<SmsRecordsDto>> response = userReachRemoteService.querySmsRecordList(request);
        log.info("response={}",response);
    }

    @Test
    public void queryCallList(){
        QueryCallListRequest request = new QueryCallListRequest();
        request.setOriginMobile("15018466088");
        request.setAppKey("obcc_system");
        request.setPageNumber(1);
        request.setPageSize(2);
        request.setStartTime("2023-12-01 00:00:00");
        request.setEndTime("2023-12-31 00:00:00");

        ApiResponse<Paging<CallRecordDto>> response = userReachRemoteService.queryCallRecordList(request);
        log.info("call list response={}",response);
    }
}

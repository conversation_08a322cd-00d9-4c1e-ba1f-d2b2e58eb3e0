package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.FeeRateViewDto;
import com.xinfei.vocmng.biz.rr.dto.LoanListDto;
import com.xinfei.vocmng.biz.rr.request.ConfirmFeeRateRequest;
import com.xinfei.vocmng.biz.rr.request.FeeRateViewRequest;
import com.xinfei.vocmng.biz.rr.request.FuturePrincipalCheckRequest;
import com.xinfei.vocmng.biz.rr.request.LoanListRequest;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Offer管理平台API测试
 *
 * <AUTHOR>
 * @version $ OfferManageApiTest, v 0.1 2025-07-21 shaohui.chen Exp $
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class OfferManageApiTest {

    @Resource
    private OfferManageApi offerManageApi;

    @Test
    public void testQueryLoanList() {
        LoanListRequest request = new LoanListRequest();
        Map<String, String> loanOrderNos = new HashMap<>();
        loanOrderNos.put("2024012800002600000004461856", "ORDER123456");
        request.setLoanOrderNos(loanOrderNos);

        ApiResponse<List<LoanListDto>> response = offerManageApi.queryLoanList(request);
        log.info("借据列表查询结果: {}", JsonUtil.toJson(response));
    }

    @Test
    public void testCheckFuturePrincipalLessThanDeduction() {
        FuturePrincipalCheckRequest request = new FuturePrincipalCheckRequest();
        request.setLoanNo("2024012800002600000004461856");
        request.setTargetFeeRatio(new BigDecimal("24")); // 24%

        ApiResponse<Boolean> response = offerManageApi.checkFuturePrincipalLessThanDeduction(request);
        log.info("未来期本金检查结果: {}", JsonUtil.toJson(response));

        if (response.isSuccess()) {
            log.info("借据号: {}, 目标费率: {}%, 未来期本金是否小于减免金额: {}",
                    request.getLoanNo(), request.getTargetFeeRatio(), response.getData());
        }
    }

    @Test
    public void testCheckFuturePrincipalWithDifferentRates() {
        String loanNo = "2024012800002600000004461856";
        BigDecimal[] targetRates = {new BigDecimal("20"), new BigDecimal("24"), new BigDecimal("30")};

        for (BigDecimal rate : targetRates) {
            FuturePrincipalCheckRequest request = new FuturePrincipalCheckRequest();
            request.setLoanNo(loanNo);
            request.setTargetFeeRatio(rate);

            ApiResponse<Boolean> response = offerManageApi.checkFuturePrincipalLessThanDeduction(request);
            log.info("借据号: {}, 目标费率: {}%, 检查结果: {}",
                    loanNo, rate, response.isSuccess() ? response.getData() : "失败");
        }
    }

    @Test
    public void testConfirmFeeRate() {
        ConfirmFeeRateRequest request = new ConfirmFeeRateRequest();

        Map<String, BigDecimal> loanFeeRates = new HashMap<>();
        loanFeeRates.put("2024012800002600000004461856", new BigDecimal("24")); // 24%
        loanFeeRates.put("2024012800002600000004461857", new BigDecimal("20")); // 20%
        request.setLoanFeeRates(loanFeeRates);

        ApiResponse<String> response = offerManageApi.confirmFeeRate(request);
        log.info("确认费率结果（包含记录插入）: {}", JsonUtil.toJson(response));

        if (response.isSuccess()) {
            log.info("确认费率成功，方案ID: {}，已插入费率应用记录", response.getData());
        }
    }

    @Test
    public void testConfirmFeeRateSingle() {
        ConfirmFeeRateRequest request = new ConfirmFeeRateRequest();

        Map<String, BigDecimal> loanFeeRates = new HashMap<>();
        loanFeeRates.put("2024012800002600000004461856", new BigDecimal("18")); // 18%
        request.setLoanFeeRates(loanFeeRates);

        ApiResponse<String> response = offerManageApi.confirmFeeRate(request);
        log.info("单借据确认费率结果: {}", JsonUtil.toJson(response));

        if (response.isSuccess()) {
            log.info("单借据确认费率成功，方案ID: {}", response.getData());
        }
    }

    @Test
    public void testQueryFeeRateList() {
        FeeRateViewRequest request = new FeeRateViewRequest();

        List<FeeRateViewRequest.LoanOrderInfo> loanOrderList = new ArrayList<>();

        FeeRateViewRequest.LoanOrderInfo info1 = new FeeRateViewRequest.LoanOrderInfo();
        info1.setLoanNo("2024012800002600000004461856");
        info1.setOrderNo("ORDER123456");
        loanOrderList.add(info1);

        FeeRateViewRequest.LoanOrderInfo info2 = new FeeRateViewRequest.LoanOrderInfo();
        info2.setLoanNo("2024012800002600000004461857");
        info2.setOrderNo("ORDER123457");
        loanOrderList.add(info2);

        request.setLoanOrderList(loanOrderList);

        ApiResponse<List<FeeRateViewDto>> response = offerManageApi.queryFeeRateList(request);
        log.info("费率查看结果: {}", JsonUtil.toJson(response));

        if (response.isSuccess() && response.getData() != null) {
            for (FeeRateViewDto dto : response.getData()) {
                log.info("订单号: {}, 应用费率: {}%, 方案明细编号: {}, 优先逾期或最近期未还金额: {}, 整笔剩余计划应还: {}, 已还总额: {}",
                        dto.getOrderNo(), dto.getAppliedFeeRate(), dto.getPlanDetailId(),
                        dto.getNearestPlanAmount(), dto.getTotalRemainingAmount(), dto.getTotalPaidAmount());

                // 验证新逻辑：优先取逾期账单，如果没有逾期则取最近期要还的
                if (dto.getNearestPlanAmount() != null && dto.getNearestPlanAmount().compareTo(BigDecimal.ZERO) > 0) {
                    log.info("找到未还金额（优先逾期或最近期）: {}", dto.getNearestPlanAmount());
                } else {
                    log.info("没有找到未还金额，可能没有未还账单");
                }
            }
        }
    }

    @Test
    public void testQueryFeeRateListSingle() {
        FeeRateViewRequest request = new FeeRateViewRequest();

        List<FeeRateViewRequest.LoanOrderInfo> loanOrderList = new ArrayList<>();

        FeeRateViewRequest.LoanOrderInfo info = new FeeRateViewRequest.LoanOrderInfo();
        info.setLoanNo("2024012800002600000004461856");
        info.setOrderNo("ORDER123456");
        loanOrderList.add(info);

        request.setLoanOrderList(loanOrderList);

        ApiResponse<List<FeeRateViewDto>> response = offerManageApi.queryFeeRateList(request);
        log.info("单借据费率查看结果: {}", JsonUtil.toJson(response));

        if (response.isSuccess() && response.getData() != null && !response.getData().isEmpty()) {
            FeeRateViewDto dto = response.getData().get(0);
            log.info("单借据详情 - 订单号: {}, 应用费率: {}%, 最近期计划应还: {}, 整笔剩余计划应还: {}, 已还总额: {}",
                    dto.getOrderNo(), dto.getAppliedFeeRate(),
                    dto.getNearestPlanAmount(), dto.getTotalRemainingAmount(), dto.getTotalPaidAmount());
        }
    }
}

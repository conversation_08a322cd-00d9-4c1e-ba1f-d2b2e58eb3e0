package com.xinfei.vocmng.biz.remote;

import cn.hutool.json.JSONUtil;
import com.xinfei.contractcore.common.service.facade.vo.ContractVO;
import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.biz.constants.ApolloConstant;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.request.DocumentRecordReq;
import com.xinfei.vocmng.biz.rr.request.LookFileResp;
import com.xinfei.vocmng.biz.service.DocumentRecordService;
import com.xinfei.vocmng.dal.po.DocumentRecord;
import com.xinfei.vocmng.itl.client.feign.ContractCoreClient;
import com.xinfei.vocmng.itl.rr.LoanInfoRequest;
import com.xinfei.vocmng.itl.rr.SettlementCertificateReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 居间协议功能测试类
 * 
 * <AUTHOR>
 * @version $ ContractRemoteServiceIntermediaryTest, v 0.1 2025/6/24 ContractRemoteServiceIntermediaryTest Exp $
 */
@Slf4j
public class ContractRemoteServiceIntermediaryTest extends TechplayDevTestBase {

    @Autowired
    private ContractRemoteService contractRemoteService;

    @MockBean
    private ContractCoreClient contractCoreClient;

    @Resource
    private DocumentRecordService documentRecordService;

    /**
     * 测试居间协议发送 - 成功场景
     */
    @Test
    public void testSendIntermediaryAgreementSuccess() {
        // 准备测试数据
        SettlementCertificateReq req = new SettlementCertificateReq();
        req.setMail("<EMAIL>");
        req.setUserNo("1639203089096191247");
        req.setCustNo("CTL0314a73126897406316dce41b47036b73");
        
        LoanInfoRequest order1 = new LoanInfoRequest();
        order1.setOrderNumber("20240328161111021506484");
        order1.setApp("xyf01");
        order1.setCapitalPoolStr("test_capital");
        order1.setLoanAmount("10000");
        
        LoanInfoRequest order2 = new LoanInfoRequest();
        order2.setOrderNumber("20240328161111021506485");
        order2.setApp("xyf01");
        order2.setCapitalPoolStr("test_capital");
        order2.setLoanAmount("20000");
        
        req.setOrders(Arrays.asList(order1, order2));

        // Mock 居间协议存在的情况
        ContractVO contractVO1 = new ContractVO();
        contractVO1.setContractName("居间服务协议");
        contractVO1.setLegacyContractKey("xyf264");
        contractVO1.setDownloadUrl("http://test.com/contract1.pdf");
        
        ContractVO contractVO2 = new ContractVO();
        contractVO2.setContractName("居间服务协议");
        contractVO2.setLegacyContractKey("xyf264");
        contractVO2.setDownloadUrl("http://test.com/contract2.pdf");

        when(contractCoreClient.listQuery(any())).thenReturn(Arrays.asList(contractVO1, contractVO2));

        // 执行测试
        ApiResponse<Boolean> response = contractRemoteService.sendIntermediaryAgreement(req);

        // 验证结果
        log.info("居间协议发送成功测试结果: {}", JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
        assertTrue("发送应该成功", response.isSuccess());
        assertTrue("返回结果应为true", response.getData());
    }

    /**
     * 测试居间协议发送 - 部分订单无居间协议
     */
    @Test
    public void testSendIntermediaryAgreementMissingContracts() {
        // 准备测试数据
        SettlementCertificateReq req = new SettlementCertificateReq();
        req.setMail("<EMAIL>");
        req.setUserNo("1639203089096191247");
        
        LoanInfoRequest order1 = new LoanInfoRequest();
        order1.setOrderNumber("20240328161111021506484");
        order1.setApp("xyf01");
        
        LoanInfoRequest order2 = new LoanInfoRequest();
        order2.setOrderNumber("20240328161111021506485");
        order2.setApp("xyf01");
        
        req.setOrders(Arrays.asList(order1, order2));

        // Mock 第一个订单有居间协议，第二个订单没有
        ContractVO contractVO1 = new ContractVO();
        contractVO1.setContractName("居间服务协议");
        contractVO1.setLegacyContractKey("xyf264");
        contractVO1.setDownloadUrl("http://test.com/contract1.pdf");

        ContractVO otherContract = new ContractVO();
        otherContract.setContractName("其他合同");
        otherContract.setLegacyContractKey("other");

        when(contractCoreClient.listQuery(any()))
            .thenReturn(Arrays.asList(contractVO1))  // 第一个订单有居间协议
            .thenReturn(Arrays.asList(otherContract)); // 第二个订单没有居间协议

        // 执行测试
        ApiResponse<Boolean> response = contractRemoteService.sendIntermediaryAgreement(req);

        // 验证结果
        log.info("部分订单无居间协议测试结果: {}", JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
        assertFalse("发送应该失败", response.isSuccess());
        assertTrue("错误信息应包含订单号", response.getMsg().contains("20240328161111021506485"));
        assertTrue("错误信息应包含提示文本", response.getMsg().contains("无居间协议"));
    }

    /**
     * 测试居间协议发送 - 空订单列表
     */
    @Test
    public void testSendIntermediaryAgreementEmptyOrders() {
        // 准备测试数据
        SettlementCertificateReq req = new SettlementCertificateReq();
        req.setMail("<EMAIL>");
        req.setOrders(Collections.emptyList());

        // 执行测试
        ApiResponse<Boolean> response = contractRemoteService.sendIntermediaryAgreement(req);

        // 验证结果
        log.info("空订单列表测试结果: {}", JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
        assertFalse("发送应该失败", response.isSuccess());
        assertEquals("错误信息应正确", "订单列表不能为空", response.getMsg());
    }

    /**
     * 测试查看居间协议文件 - 成功场景
     */
    @Test
    public void testLookIntermediaryAgreementSuccess() {
        // 准备测试数据
        DocumentRecordReq req = new DocumentRecordReq();
        req.setId(1L);
        req.setOrderNumber("20240328161111021506484");
        req.setUserNo("1639203089096191247");
        req.setType(5); // 居间协议类型

        // Mock 居间协议存在
        ContractVO contractVO = new ContractVO();
        contractVO.setContractName("居间服务协议");
        contractVO.setLegacyContractKey("xyf264");
        contractVO.setDownloadUrl("http://test.com/intermediary-agreement.pdf");

        when(contractCoreClient.listQuery(any())).thenReturn(Arrays.asList(contractVO));

        // 执行测试
        ApiResponse<LookFileResp> response = contractRemoteService.lookFile(req);

        // 验证结果
        log.info("查看居间协议成功测试结果: {}", JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
        assertTrue("查看应该成功", response.isSuccess());
        assertNotNull("文件URL不应为空", response.getData().getUrl());
        assertFalse("文件URL列表不应为空", response.getData().getUrl().isEmpty());
        assertEquals("文件URL应正确", "http://test.com/intermediary-agreement.pdf", response.getData().getUrl().get(0));
    }

    /**
     * 测试查看居间协议文件 - 文件不存在
     */
    @Test
    public void testLookIntermediaryAgreementNotFound() {
        // 准备测试数据
        DocumentRecordReq req = new DocumentRecordReq();
        req.setId(1L);
        req.setOrderNumber("20240328161111021506484");
        req.setUserNo("1639203089096191247");
        req.setType(5); // 居间协议类型

        // Mock 没有居间协议
        when(contractCoreClient.listQuery(any())).thenReturn(Collections.emptyList());

        // 执行测试
        ApiResponse<LookFileResp> response = contractRemoteService.lookFile(req);

        // 验证结果
        log.info("居间协议不存在测试结果: {}", JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
        assertFalse("查看应该失败", response.isSuccess());
        assertTrue("错误信息应包含提示", response.getMsg().contains("居间协议不存在"));
    }

    /**
     * 测试重新发送居间协议文件
     */
    @Test
    public void testResendIntermediaryAgreement() {
        // 准备测试数据 - 创建一个已存在的居间协议记录
        DocumentRecord record = DocumentRecord.builder()
                .orderNo("20240328161111021506484")
                .userNo("1639203089096191247")
                .createUser("test_user")
                .type(5) // 居间协议类型
                .mail("<EMAIL>")
                .status(2) // 已发送
                .capitalPool("test_capital")
                .prinAmt(new BigDecimal("10000"))
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        // 保存记录到数据库
        documentRecordService.saveBatchs(Arrays.asList(record));

        // 准备重新发送请求
        DocumentRecordReq req = new DocumentRecordReq();
        req.setId(record.getId());
        req.setOrderNumber("20240328161111021506484");
        req.setUserNo("1639203089096191247");
        req.setType(5);
        req.setMail("<EMAIL>");

        // Mock 居间协议存在
        ContractVO contractVO = new ContractVO();
        contractVO.setContractName("居间服务协议");
        contractVO.setLegacyContractKey("xyf264");
        contractVO.setDownloadUrl("http://test.com/intermediary-agreement.pdf");

        when(contractCoreClient.listQuery(any())).thenReturn(Arrays.asList(contractVO));

        // 执行测试
        ApiResponse<String> response = contractRemoteService.sendFile(req);

        // 验证结果
        log.info("重新发送居间协议测试结果: {}", JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
        assertTrue("重新发送应该成功", response.isSuccess());
    }


    /**
     * 测试居间协议检查逻辑 - 多种合同类型混合
     */
    @Test
    public void testCheckIntermediaryAgreementMixedContracts() {
        // 准备测试数据
        SettlementCertificateReq req = new SettlementCertificateReq();
        req.setMail("<EMAIL>");
        req.setUserNo("1639203089096191247");

        LoanInfoRequest order1 = new LoanInfoRequest();
        order1.setOrderNumber("20240328161111021506484");
        order1.setApp("xyf01");

        LoanInfoRequest order2 = new LoanInfoRequest();
        order2.setOrderNumber("20240328161111021506485");
        order2.setApp("xyf01");

        LoanInfoRequest order3 = new LoanInfoRequest();
        order3.setOrderNumber("20240328161111021506486");
        order3.setApp("xyf01");

        req.setOrders(Arrays.asList(order1, order2, order3));

        // Mock 不同的合同情况
        // 订单1：有居间协议
        ContractVO intermediaryContract = new ContractVO();
        intermediaryContract.setContractName("居间服务协议");
        intermediaryContract.setLegacyContractKey("xyf264");
        intermediaryContract.setDownloadUrl("http://test.com/intermediary.pdf");

        // 订单2：有其他合同但没有居间协议
        ContractVO otherContract = new ContractVO();
        otherContract.setContractName("借款合同");
        otherContract.setLegacyContractKey("loan001");

        // 订单3：没有任何合同
        when(contractCoreClient.listQuery(any()))
            .thenReturn(Arrays.asList(intermediaryContract))  // 订单1有居间协议
            .thenReturn(Arrays.asList(otherContract))         // 订单2有其他合同
            .thenReturn(Collections.emptyList());             // 订单3没有合同

        // 执行测试
        ApiResponse<Boolean> response = contractRemoteService.sendIntermediaryAgreement(req);

        // 验证结果
        log.info("混合合同类型测试结果: {}", JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
        assertFalse("发送应该失败", response.isSuccess());
        assertTrue("错误信息应包含订单号2", response.getMsg().contains("20240328161111021506485"));
        assertTrue("错误信息应包含订单号3", response.getMsg().contains("20240328161111021506486"));
        assertFalse("错误信息不应包含订单号1", response.getMsg().contains("20240328161111021506484"));
    }

    /**
     * 测试居间协议URL获取 - 多个居间协议文件
     */
    @Test
    public void testGetMultipleIntermediaryAgreementUrls() {
        // 准备测试数据
        SettlementCertificateReq req = new SettlementCertificateReq();
        req.setMail("<EMAIL>");
        req.setUserNo("1639203089096191247");
        req.setCustNo("CTL0314a73126897406316dce41b47036b73");

        LoanInfoRequest order = new LoanInfoRequest();
        order.setOrderNumber("20240328161111021506484");
        order.setApp("xyf01");
        order.setCapitalPoolStr("test_capital");
        order.setLoanAmount("10000");

        req.setOrders(Arrays.asList(order));

        // Mock 多个居间协议文件
        ContractVO contract1 = new ContractVO();
        contract1.setContractName("居间服务协议");
        contract1.setLegacyContractKey("xyf264");
        contract1.setDownloadUrl("http://test.com/intermediary1.pdf");

        ContractVO contract2 = new ContractVO();
        contract2.setContractName("居间服务协议");
        contract2.setLegacyContractKey("xyf264");
        contract2.setDownloadUrl("http://test.com/intermediary2.pdf");

        ContractVO otherContract = new ContractVO();
        otherContract.setContractName("其他合同");
        otherContract.setLegacyContractKey("other");
        otherContract.setDownloadUrl("http://test.com/other.pdf");

        when(contractCoreClient.listQuery(any()))
            .thenReturn(Arrays.asList(contract1, contract2, otherContract));

        // 执行测试
        ApiResponse<Boolean> response = contractRemoteService.sendIntermediaryAgreement(req);

        // 验证结果
        log.info("多个居间协议文件测试结果: {}", JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
        assertTrue("发送应该成功", response.isSuccess());
        assertTrue("返回结果应为true", response.getData());
    }

    /**
     * 测试居间协议发送 - 邮件配置异常
     */
    @Test
    public void testSendIntermediaryAgreementMailConfigError() {
        // 准备测试数据 - 使用不支持的app
        SettlementCertificateReq req = new SettlementCertificateReq();
        req.setMail("<EMAIL>");
        req.setUserNo("1639203089096191247");

        LoanInfoRequest order = new LoanInfoRequest();
        order.setOrderNumber("20240328161111021506484");
        order.setApp("unsupported_app"); // 不支持的app
        order.setCapitalPoolStr("test_capital");
        order.setLoanAmount("10000");

        req.setOrders(Arrays.asList(order));

        // Mock 居间协议存在
        ContractVO contractVO = new ContractVO();
        contractVO.setContractName("居间服务协议");
        contractVO.setLegacyContractKey("xyf264");
        contractVO.setDownloadUrl("http://test.com/contract.pdf");

        when(contractCoreClient.listQuery(any())).thenReturn(Arrays.asList(contractVO));

        // 执行测试
        ApiResponse<Boolean> response = contractRemoteService.sendIntermediaryAgreement(req);

        // 验证结果
        log.info("邮件配置异常测试结果: {}", JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
        assertFalse("发送应该失败", response.isSuccess());
        assertTrue("错误信息应包含邮件配置相关内容",
            response.getMsg().contains("mail config") || response.getMsg().contains("邮件配置"));
    }

    /**
     * 测试DocumentRecord保存 - 居间协议类型
     */
    @Test
    public void testSaveIntermediaryDocumentRecord() {
        // 准备测试数据
        DocumentRecord record = DocumentRecord.builder()
                .orderNo("20240328161111021506484")
                .userNo("1639203089096191247")
                .createUser("test_user")
                .type(5) // 居间协议类型
                .mail("<EMAIL>")
                .status(2) // 已发送
                .capitalPool("test_capital")
                .prinAmt(new BigDecimal("10000"))
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        // 执行测试
        documentRecordService.saveBatchs(Arrays.asList(record));

        // 验证结果
        log.info("居间协议DocumentRecord保存测试完成，记录ID: {}", record.getId());
        assertNotNull("记录ID不应为空", record.getId());
        assertEquals("类型应为5", Integer.valueOf(5), record.getType());
        assertEquals("状态应为2", Integer.valueOf(2), record.getStatus());
    }

    /**
     * 测试居间协议发送 - 合同查询异常
     */
    @Test
    public void testSendIntermediaryAgreementContractQueryException() {
        // 准备测试数据
        SettlementCertificateReq req = new SettlementCertificateReq();
        req.setMail("<EMAIL>");
        req.setUserNo("1639203089096191247");

        LoanInfoRequest order = new LoanInfoRequest();
        order.setOrderNumber("20240328161111021506484");
        order.setApp("xyf01");

        req.setOrders(Arrays.asList(order));

        // Mock 合同查询异常
        when(contractCoreClient.listQuery(any())).thenThrow(new RuntimeException("合同服务异常"));

        // 执行测试
        ApiResponse<Boolean> response = contractRemoteService.sendIntermediaryAgreement(req);

        // 验证结果
        log.info("合同查询异常测试结果: {}", JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
        assertFalse("发送应该失败", response.isSuccess());
        assertTrue("错误信息应包含订单号", response.getMsg().contains("20240328161111021506484"));
    }
}

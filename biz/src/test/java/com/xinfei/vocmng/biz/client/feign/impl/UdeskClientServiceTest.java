package com.xinfei.vocmng.biz.client.feign.impl;

import com.xinfei.vocmng.itl.client.feign.impl.UdeskClientService;
import com.xinfei.vocmng.itl.client.http.UdeskClient;
import com.xinfei.vocmng.itl.rr.CreateCustomerResponse;
import com.xinfei.vocmng.itl.rr.Customer;
import com.xinfei.vocmng.itl.rr.ExportCustomerResponse;
import com.xinfei.vocmng.itl.rr.GetCustomerFilterListResponse;
import com.xinfei.vocmng.itl.rr.GetCustomerListResponse;
import com.xinfei.vocmng.itl.rr.MergeCustomerResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * UdeskClientService的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class UdeskClientServiceTest {

    @Mock
    private UdeskClient udeskClient;

    @InjectMocks
    private UdeskClientService udeskClientService;

    // 不需要设置静态常量，因为它们是static final的，无法通过反射修改

    @Test
    @DisplayName("测试获取客户列表")
    public void testGetCustomerList() {
        // 准备模拟数据
        GetCustomerListResponse mockResponse = new GetCustomerListResponse();
        mockResponse.setCode(1000);
        List<GetCustomerListResponse.Customer> customers = new ArrayList<>();
        GetCustomerListResponse.Customer customer = new GetCustomerListResponse.Customer();
        customer.setId(123L);
        customer.setNickName("测试用户");
        customers.add(customer);
        mockResponse.setCustomers(customers);

        GetCustomerListResponse.Meta meta = new GetCustomerListResponse.Meta();
        meta.setTotalPages(1);
        mockResponse.setMeta(meta);

        // 设置模拟行为
        doReturn(mockResponse).when(udeskClient).getCustomerList(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
        );

        // 执行测试
        GetCustomerListResponse response = udeskClientService.getCustomerList(1, 10);

        // 验证结果
        assertNotNull(response);
        assertEquals(1000, response.getCode());
        assertNotNull(response.getCustomers());
        assertEquals(1, response.getCustomers().size());
        assertEquals(123L, response.getCustomers().get(0).getId());
        assertEquals("测试用户", response.getCustomers().get(0).getNickName());
    }

    @Test
    @DisplayName("测试获取所有客户")
    public void testGetAllCustomers() {
        // 准备模拟数据
        GetCustomerListResponse mockResponse = new GetCustomerListResponse();
        mockResponse.setCode(1000);
        List<GetCustomerListResponse.Customer> customers = new ArrayList<>();
        GetCustomerListResponse.Customer customer = new GetCustomerListResponse.Customer();
        customer.setId(123L);
        customer.setNickName("测试用户");
        customers.add(customer);
        mockResponse.setCustomers(customers);

        GetCustomerListResponse.Meta meta = new GetCustomerListResponse.Meta();
        meta.setTotalPages(1);
        mockResponse.setMeta(meta);

        // 设置模拟行为
        doReturn(mockResponse).when(udeskClient).getCustomerList(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
        );

        // 执行测试
        List<GetCustomerListResponse.Customer> allCustomers = udeskClientService.getAllCustomers();

        // 验证结果
        assertNotNull(allCustomers);
        assertEquals(1, allCustomers.size());
        assertEquals(123L, allCustomers.get(0).getId());
        assertEquals("测试用户", allCustomers.get(0).getNickName());
    }

    @Test
    @DisplayName("测试客户批量导出")
    public void testExportCustomers() {
        // 准备模拟数据
        ExportCustomerResponse mockResponse = new ExportCustomerResponse();
        mockResponse.setCode(1000);
        mockResponse.setScrollId("test-scroll-id");
        mockResponse.setTotal(100L);
        List<Customer> customers = new ArrayList<>();
        Customer customer = new Customer();
        customer.setId(123L);
        customer.setNickName("测试用户");
        customers.add(customer);
        mockResponse.setCustomers(customers);

        // 设置模拟行为
        // 使用doReturn().when()语法而不是when().thenReturn()语法，避免参数严格匹配问题
        doReturn(mockResponse).when(udeskClient).exportCustomers(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
        );

        // 执行测试
        ExportCustomerResponse response = udeskClientService.exportCustomers(1L, "test", null);

        // 验证结果
        assertNotNull(response);
        assertEquals(1000, response.getCode());
        assertEquals("test-scroll-id", response.getScrollId());
        assertEquals(100, response.getTotal());
        assertNotNull(response.getCustomers());
        assertEquals(1, response.getCustomers().size());
        assertEquals(123L, response.getCustomers().get(0).getId());
        assertEquals("测试用户", response.getCustomers().get(0).getNickName());
    }

    @Test
    @DisplayName("测试获取客户过滤器列表")
    public void testGetCustomerFilters() {
        // 准备模拟数据
        GetCustomerFilterListResponse mockResponse = new GetCustomerFilterListResponse();
        mockResponse.setCode(1000);
        List<GetCustomerFilterListResponse.CustomerFilter> filters = new ArrayList<>();
        GetCustomerFilterListResponse.CustomerFilter filter = new GetCustomerFilterListResponse.CustomerFilter();
        filter.setId(1);
        filter.setName("测试过滤器");
        filter.setActive(true);
        filters.add(filter);
        mockResponse.setCustomerFilters(filters);

        // 设置模拟行为
        doReturn(mockResponse).when(udeskClient).getCustomerFilters(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
        );

        // 执行测试
        GetCustomerFilterListResponse response = udeskClientService.getCustomerFilters();

        // 验证结果
        assertNotNull(response);
        assertEquals(1000, response.getCode());
        assertNotNull(response.getCustomerFilters());
        assertEquals(1, response.getCustomerFilters().size());
        assertEquals(1, response.getCustomerFilters().get(0).getId());
        assertEquals("测试过滤器", response.getCustomerFilters().get(0).getName());
        assertTrue(response.getCustomerFilters().get(0).isActive());
    }

    @Test
    @DisplayName("测试合并客户")
    public void testMergeCustomers() {
        // 准备模拟数据
        MergeCustomerResponse mockResponse = new MergeCustomerResponse();
        mockResponse.setCode(1000);
        mockResponse.setId(456L);

        // 设置模拟行为
        doReturn(mockResponse).when(udeskClient).mergeCustomers(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
        );

        // 执行测试
        MergeCustomerResponse response = udeskClientService.mergeCustomers(
                "cellphone", "13800138000",
                "cellphone", "13900139000"
        );

        // 验证结果
        assertNotNull(response);
        assertEquals(1000, response.getCode());
        assertEquals(456L, response.getId());
    }

    @Test
    @DisplayName("测试客户批量导出失败情况")
    public void testExportCustomersFailure() {
        // 准备模拟数据
        ExportCustomerResponse mockResponse = new ExportCustomerResponse();
        mockResponse.setCode(1001); // 非成功状态码

        // 设置模拟行为
        // 使用doReturn().when()语法而不是when().thenReturn()语法，避免参数严格匹配问题
        doReturn(mockResponse).when(udeskClient).exportCustomers(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
        );

        // 执行测试
        ExportCustomerResponse response = udeskClientService.exportCustomers(1L, "test", null);

        // 验证结果
        assertNull(response);
    }

    @Test
    @DisplayName("测试获取客户过滤器列表失败情况")
    public void testGetCustomerFiltersFailure() {
        // 准备模拟数据
        GetCustomerFilterListResponse mockResponse = new GetCustomerFilterListResponse();
        mockResponse.setCode(1001); // 非成功状态码

        // 设置模拟行为
        doReturn(mockResponse).when(udeskClient).getCustomerFilters(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
        );

        // 执行测试
        GetCustomerFilterListResponse response = udeskClientService.getCustomerFilters();

        // 验证结果
        assertNull(response);
    }

    @Test
    @DisplayName("测试合并客户失败情况")
    public void testMergeCustomersFailure() {
        // 准备模拟数据
        MergeCustomerResponse mockResponse = new MergeCustomerResponse();
        mockResponse.setCode(1001); // 非成功状态码

        // 设置模拟行为
        doReturn(mockResponse).when(udeskClient).mergeCustomers(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
        );

        // 执行测试
        MergeCustomerResponse response = udeskClientService.mergeCustomers(
                "cellphone", "13800138000",
                "cellphone", "13900139000"
        );

        // 验证结果
        assertNull(response);
    }

    @Test
    @DisplayName("测试创建客户")
    public void testCreateCustomer() {
        // 准备模拟数据
        CreateCustomerResponse mockResponse = new CreateCustomerResponse();
        mockResponse.setCode(1000);
        Customer customer = new Customer();
        customer.setId(123L);
        customer.setNickName("测试用户");
        mockResponse.setCustomer(customer);

        // 设置模拟行为
        doReturn(mockResponse).when(udeskClient).createCustomer(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
        );

        // 准备请求参数
        Customer requestCustomer = new Customer();
        requestCustomer.setNickName("测试用户");
        List<String> otherEmails = Collections.singletonList("<EMAIL>");
        List<String> tags = Collections.singletonList("VIP");

        // 执行测试
        CreateCustomerResponse response = udeskClientService.createCustomer(requestCustomer);

        // 验证结果
        assertNotNull(response);
        assertEquals(1000, response.getCode());
        assertNotNull(response.getCustomer());
        assertEquals(123L, response.getCustomer().getId());
        assertEquals("测试用户", response.getCustomer().getNickName());
    }

    @Test
    @DisplayName("测试创建客户失败情况")
    public void testCreateCustomerFailure() {
        // 准备模拟数据
        CreateCustomerResponse mockResponse = new CreateCustomerResponse();
        mockResponse.setCode(1001); // 非成功状态码

        // 设置模拟行为
        doReturn(mockResponse).when(udeskClient).createCustomer(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
        );

        // 准备请求参数
        Customer requestCustomer = new Customer();
        requestCustomer.setNickName("测试用户");

        // 执行测试
        CreateCustomerResponse response = udeskClientService.createCustomer(requestCustomer);

        // 验证结果
        assertNull(response);
    }
}

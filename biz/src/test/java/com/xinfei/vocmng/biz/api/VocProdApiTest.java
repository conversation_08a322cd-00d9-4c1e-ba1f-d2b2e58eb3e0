package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.request.FeeRatioProcessReq;
import com.xinfei.vocmng.biz.rr.request.FeeRatioProcessResp;
import com.xinfei.vocmng.biz.rr.request.RefundFeeRatioProcessReq;
import com.xinfei.vocmng.biz.rr.response.RefundFeeRatioProcessResp;
import com.xinfei.vocmng.biz.service.impl.VocmngService;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class VocProdApiTest {

    @Autowired
    private VocmngService vocmngService;

    @Test
    public void rateCalculation() {
        Boolean response = vocmngService.getScreenshotConfine(1939303089098213330L);
        log.info(JsonUtil.toJson(response));
    }
}
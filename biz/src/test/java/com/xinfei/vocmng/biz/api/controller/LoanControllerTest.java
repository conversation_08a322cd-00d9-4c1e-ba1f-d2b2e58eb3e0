/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api.controller;

/**
 * <AUTHOR>
 * @version $ LoanControllerTest, v 0.1 2025-04-23 11:51 junjie.yan Exp $
 */

import com.xinfei.vocmng.biz.model.enums.ResultCodeEnum;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.OrderDto;
import com.xinfei.vocmng.biz.rr.request.GetOrderListRequest;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.biz.service.LoanService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class LoanControllerTest {

    @Mock
    private LoanService loanService;

    @InjectMocks
    private LoanController controller;

    @BeforeEach
    void setUp() {
        // 初始化控制器和mock对象
    }

    @Test
    public void testGetOrderList_missingRequiredParams() {
        // 测试场景：所有必填参数为空
        GetOrderListRequest request = new GetOrderListRequest();
        // 设置所有必填参数为空
        request.setUserNos(null);
        request.setMobile(null);
        request.setIdCard(null);
        request.setOrderNos(null);
        request.setLoanNos(null);
        request.setOutOrderNumber(null);
        request.setChannelOrderNumber(null);
        request.setMobileCust(null);
        request.setCustNo(null);

        // 验证异常抛出
        IgnoreException exception = assertThrows(IgnoreException.class, () -> controller.getOrderList(request));
        assertEquals(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, exception.getResultCodeEnum());
        assertEquals("手机号/身份证号/用户号/订单号/资方订单号/渠道订单号/借据号 必传其一", exception.getMsg());
    }

    @Test
    public void testGetOrderList_loanStatusWithoutMobileOrIdCard() {
        // 测试场景：使用借据状态但未提供手机号或身份证号
        GetOrderListRequest request = new GetOrderListRequest();
        request.setLoanStatus(Collections.singletonList("RP"));
        request.setOrderNos(Collections.singletonList("123123"));
        request.setMobile(null);
        request.setIdCard(null);

        IgnoreException exception = assertThrows(IgnoreException.class, () -> controller.getOrderList(request));
        assertEquals(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, exception.getResultCodeEnum());
        assertEquals("借据状态作为筛选条件，手机号和身份证号必传其一", exception.getMsg());
    }

    @Test
    public void testGetOrderList_validOutOrderNumber() {
        // 测试场景：有效资方订单号查询
        GetOrderListRequest request = new GetOrderListRequest();
        request.setOutOrderNumber("OUT123");
        when(loanService.queryByOutOrderNumber(request)).thenReturn("ORDER123");

        // 模拟 queryOrderList 返回结果
        Paging<OrderDto> mockPaging = new Paging<>();
        when(loanService.queryOrderList(any())).thenReturn(mockPaging);

        // 调用方法并验证结果
        ApiResponse<Paging<OrderDto>> result = controller.getOrderList(request);
        assertNotNull(result);
        assertEquals(ResultCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(mockPaging, result.getData());
    }

    @Test
    public void testGetOrderList_inconsistentOrderNumbers() {
        // 测试场景：资方和渠道订单号结果不一致
        GetOrderListRequest request = new GetOrderListRequest();
        request.setOutOrderNumber("OUT123");
        request.setChannelOrderNumber("CHANNEL456");
        when(loanService.queryByOutOrderNumber(request)).thenReturn("ORDER1");
        when(loanService.queryByChannelOrderNumber(request)).thenReturn("ORDER2");

        IgnoreException exception = assertThrows(IgnoreException.class, () -> controller.getOrderList(request));
        assertEquals(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, exception.getResultCodeEnum());
        assertEquals("通过资方订单号与渠道订单号查询的订单号不一致", exception.getMsg());
    }

    @Test
    public void testGetOrderList_success() {
        // 测试场景：正常查询
        GetOrderListRequest request = new GetOrderListRequest();
        request.setUserNos(Arrays.asList("USER1"));

        // 模拟 queryOrderList 返回结果
        Paging<OrderDto> mockPaging = new Paging<>();
        when(loanService.queryOrderList(request)).thenReturn(mockPaging);

        ApiResponse<Paging<OrderDto>> result = controller.getOrderList(request);
        assertEquals(ResultCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(mockPaging, result.getData());
    }

    @Test
    public void testGetOrderList_nullIsCustomerDetail() {
        // 测试场景：isCustomerDetail 为 null 时默认设为 false
        GetOrderListRequest request = new GetOrderListRequest();
        request.setIsCustomerDetail(null);
        request.setUserNos(Arrays.asList("USER1"));

        // 捕获参数验证设置
        ArgumentCaptor<GetOrderListRequest> captor = ArgumentCaptor.forClass(GetOrderListRequest.class);
        when(loanService.queryOrderList(any())).thenReturn(new Paging<>());

        controller.getOrderList(request);
        verify(loanService).queryOrderList(captor.capture());
        assertFalse(captor.getValue().getIsCustomerDetail());
    }
}
